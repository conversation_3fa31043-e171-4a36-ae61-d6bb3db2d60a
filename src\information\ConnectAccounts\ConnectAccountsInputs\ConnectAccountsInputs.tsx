import React, { FC, memo, useCallback } from "react";
import {
  AccountCard,
  AccountType,
  facebookIcon,
  instagramIcon,
  SvgProps,
  tiktokIcon,
  twitchIcon,
  youTubeIcon
} from "@eait-playerexp-cn/core-ui-kit";
import { toast } from "react-toastify";
import { Dispatch, ErrorHandling } from "../../../utils";
import { ConnectAccountLabels } from "../../InterestedCreatorsInformationPage/InterestedCreatorsInformationPage";
import { POPUP_OPENED, WINDOW_PARAMS } from "../../../utils/constants";
import {
  GET_FB_PAGES,
  RELOAD_INTERESTED_CREATOR_ACCOUNTS,
  SHOW_FACEBOOK_PAGES
} from "../ConnectedAccount/ConnectedAccounts";
import ConnectedAccountsService from "../../../Browser/ConnectedAccountsService";
import { Configuration, ConnectAccounts } from "../../Information";

const accounts = [
  { label: "youTube", accountIcon: youTubeIcon, accountType: "YOUTUBE" },
  { label: "twitch", accountIcon: twitchIcon, accountType: "TWITCH" },
  { label: "facebook", accountIcon: facebookIcon, accountType: "FACEBOOK" },
  { label: "instagram", accountIcon: instagramIcon, accountType: "INSTAGRAM" },
  { label: "tiktok", accountIcon: tiktokIcon, accountType: "TIKTOK" }
];

export type AddAccountsProps = {
  imageType: FC<SvgProps>;
  accountType: AccountType;
  url: string;
  setShowAddConfirmation: (data: boolean) => void;
  showAddConfirmation: boolean;
  disconnected: boolean;
  isExpired: boolean;
  labels: ConnectAccountLabels;
  stableDispatch: Dispatch;
  errorHandling: ErrorHandling;
  configuration: Configuration;
};
const AddAccounts = memo(function AddAccounts({
  imageType,
  accountType,
  url,
  setShowAddConfirmation,
  showAddConfirmation,
  disconnected,
  isExpired,
  labels,
  errorHandling,
  stableDispatch,
  configuration
}: AddAccountsProps) {
  const connectedAccountsService = new ConnectedAccountsService(configuration.applicationsClient);
  const onAddAccount = useCallback(() => {
    if (!showAddConfirmation) {
      setShowAddConfirmation(true);
      const loginWindow = window.open(url, "_blank", WINDOW_PARAMS);
      toast.dismiss();
      stableDispatch({ type: POPUP_OPENED, data: true });
      const loop = setInterval(function () {
        if (loginWindow?.closed) {
          clearInterval(loop);
          stableDispatch({ type: POPUP_OPENED, data: false });
          setShowAddConfirmation(false);
          connectedAccountsService
            .clearAccountType()
            .then(() => {
              if (accountType === "FACEBOOK") {
                stableDispatch({ type: GET_FB_PAGES, data: true });
                stableDispatch({ type: SHOW_FACEBOOK_PAGES, data: true });
              }
              stableDispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: true });
            })
            .catch((e: Error) => errorHandling(stableDispatch, e as unknown as Error));
        }
      }, 100);
    }
  }, [showAddConfirmation, url, setShowAddConfirmation, stableDispatch]);

  return (
    <>
      <AccountCard
        username=""
        accountIcon={imageType}
        handleRemoveAccount={() => {}}
        expired={!disconnected && isExpired}
        noAccount={true}
        handleAddAccount={onAddAccount}
        accountType={accountType}
        labels={labels}
      />
    </>
  );
});

export type ConnectAccountsInputsProps = {
  labels: ConnectAccountLabels;
  setShowAddConfirmation: (data: boolean) => void;
  showAddConfirmation: boolean;
  stableDispatch: Dispatch;
  errorHandling: ErrorHandling;
  configuration: Configuration;
  connectAccounts: ConnectAccounts;
};
const ConnectAccountsInputs: FC<ConnectAccountsInputsProps> = ({
  labels,
  setShowAddConfirmation,
  showAddConfirmation,
  stableDispatch,
  errorHandling,
  configuration,
  connectAccounts
}): JSX.Element => {
  return (
    <div>
      <div className="mg-ic-ui-connected-accounts-container">
        <h5 className="mg-ic-ui-connected-accounts-title">{labels.addAccount}</h5>
        <div className="ic-ui-connect-account-card-container">
          {accounts.map(({ accountType }: { accountType: string }, index: number) => {
            const connectAccountInfo = connectAccounts.find(
              (connectAccount) => connectAccount.value.toLowerCase() === accountType.toLowerCase()
            );
            return (
              <AddAccounts
                key={`accounts-${index}`}
                url={connectAccountInfo?.redirectUrl as string}
                setShowAddConfirmation={setShowAddConfirmation}
                showAddConfirmation={showAddConfirmation}
                labels={labels}
                accountType={accountType as AccountType}
                imageType={connectAccountInfo?.accountIcon as FC<SvgProps>}
                stableDispatch={stableDispatch}
                errorHandling={errorHandling}
                isExpired={false}
                disconnected={false}
                configuration={configuration}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
};
export default ConnectAccountsInputs;
