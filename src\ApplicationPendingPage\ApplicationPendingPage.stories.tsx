import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import ApplicationPendingPage from "./ApplicationPendingPage";

const meta: Meta<typeof ApplicationPendingPage> = {
  title: "Component Library/Application Pending Component",
  component: ApplicationPendingPage,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ApplicationPendingPage>;

const labels = {
  title: "Submission Complete",
  subTitle: "Thank you for submitting your request to join the EA Support a Creator Program.",
  applicationPendingDescription:
    "Your submission has been sent for review by a member of the EA Support a Creator Team. If you are not accepted, you’ll recieve an email with an invitation to join!",
  description:
    "We have already received your submission request to join the Creator Network from this Electronic Arts account. You can view the status of your submission below:",
  returnToHomePage: "Back Home",
  pending: "Pending",
  unReviewed: "Unreviewed",
  submissionReceived: "Submission Received",
  submissionReceivedDescription:
    "We have received your submission request to join the EA Creator Network from this EA account. Please check back later for an update on your submission status. We receive a large number of submission requests on a regular basis so if your submission has not been reviewed after 90 days, you may be eligible to resubmit.",
  submissionUpdate: "Still interested in joining?",
  submissionUpdateDescription:
    "Due to the high volume of submissions, your previous submission has not yet been reviewed. If you are still interested in joining EA Creator Network, please review and update your information to resubmit.",
  reviewAndResubmit: "Review & Resubmit",
  email: "Email",
  programLabel: "Program",
  programName: "Support a Creator",
  status: "Status",
  submissionDate: "Submission Date",
  close: "Close",
  creatorNetwork: "Creator Network",
  applicationPendingThumbnailAltText: "Application Pending Thumbnail"
};

export const ApplicationPendingComponentWithInterestedCreatorCanReapply: Story = {
  args: {
    labels: labels,
    locale: "en-us",
    INTERESTED_CREATOR_REAPPLY_PERIOD: true,
    analytics: { checkedApplicationStatus: () => {} },
    emailId: "<EMAIL>",
    canApply: true,
    submittedDate: "2023-01-01",
    redirectedToInformationForm: "/",
    redirectedToMain: "/",
    applicationPendingThumbnail: "./img/Players-comp.png"
  }
};

export const ApplicationPendingComponentWithInterestedCreatorCanNotReapply: Story = {
  args: {
    labels: labels,
    locale: "en-us",
    INTERESTED_CREATOR_REAPPLY_PERIOD: true,
    analytics: { checkedApplicationStatus: () => {} },
    emailId: "<EMAIL>",
    canApply: false,
    submittedDate: "2023-01-01",
    redirectedToInformationForm: "/",
    redirectedToMain: "/",
    applicationPendingThumbnail: "./img/Players-comp.png"
  }
};

export const ApplicationPendingComponent: Story = {
  args: {
    labels: labels,
    locale: "en-us",
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    analytics: { checkedApplicationStatus: () => {} },
    emailId: "<EMAIL>",
    canApply: true,
    submittedDate: "2023-01-01",
    redirectedToInformationForm: "/",
    redirectedToMain: "/",
    applicationPendingThumbnail: "./img/Players-comp.png"
  }
};
