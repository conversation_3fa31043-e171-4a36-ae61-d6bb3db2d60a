import React, { memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactElement, ReactNode, useCallback, useEffect, useState } from "react";
import { FormLabels, InterestedCreatorsCreatorType, PageLabels } from "../CreatorType";
import {
  <PERSON>rowser<PERSON>nalytics,
  CloseHandler,
  ERROR,
  ErrorHandling,
  InterestedCreator,
  LOADING,
  onToastClose,
  State,
  toastContent,
  VALIDATION_ERROR,
  ValidationError
} from "../../utils";
import { NextRouter } from "next/router";
import { Dispatch } from "../../utils";
import InterestedCreatorsCreatorTypeForm, {
  InterestedCreatorsCreatorTypeFormLabels
} from "../InterestedCreatorsCreatorTypeForm/InterestedCreatorsCreatorTypeForm";
import { Toast } from "@eait-playerexp-cn/core-ui-kit";
import CancelRegistrationModal from "../../CancelRegistrationModal/CancelRegistrationModal";
import { Configuration } from "../../information/Information";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import { CreatorType } from "@eait-playerexp-cn/metadata-types";

export const getCreatorsTypes = async (
  metadataService: MetadataService,
  stableDispatch: Dispatch,
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean,
  interestedCreator: InterestedCreator,
  errorHandling: ErrorHandling
): Promise<Array<CreatorType> | void> => {
  try {
    stableDispatch({ type: LOADING, data: true });
    const creatorTypes = await metadataService.getCreatorTypes();
    if (INTERESTED_CREATOR_REAPPLY_PERIOD) {
      const interestedCreatorsCreatorType: Array<CreatorType> = [];
      (interestedCreator as unknown as { creatorTypes: Array<CreatorType> }).creatorTypes.forEach(
        (item: CreatorType) => {
          const foundType = creatorTypes.find((creatorType) => creatorType.value === item.value);
          if (foundType) interestedCreatorsCreatorType.push(foundType);
        }
      );
      (interestedCreator as unknown as { creatorTypes: Array<CreatorType> }).creatorTypes =
        interestedCreatorsCreatorType;
    }
    stableDispatch({ type: LOADING, data: false });
    return creatorTypes as Array<CreatorType>;
  } catch (e) {
    stableDispatch({ type: LOADING, data: false });
    errorHandling(stableDispatch, e as unknown as Error);
  }
};

export type InterestedCreatorsCreatorTypePageProps = {
  formLabels: FormLabels & InterestedCreatorsCreatorTypeFormLabels;
  pageLabels: PageLabels;
  showConfirmation?: boolean;
  children?: ReactNode;
  interestedCreator?: InterestedCreatorsCreatorType;
  state: State;
  stableDispatch: Dispatch;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  unhandledError?: string;
  setShowConfirmation: (a: boolean) => void;
  router: NextRouter;
  handleCancelRegistration: () => void;
  analytics: BrowserAnalytics;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  errorHandling: ErrorHandling;
  onClose: MouseEventHandler<HTMLButtonElement>;
  redirectedToNextStepUrl: string;
  configuration: Configuration;
  basePath?: string;
};

export default memo(function InterestedCreatorsCreatorTypePage({
  formLabels,
  pageLabels,
  onClose,
  showConfirmation = false,
  interestedCreator,
  stableDispatch,
  unhandledError = "",
  state,
  errorToast,
  setShowConfirmation,
  router,
  handleCancelRegistration,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  errorHandling,
  redirectedToNextStepUrl,
  configuration,
  basePath
}: InterestedCreatorsCreatorTypePageProps) {
  const { interestedCreatorTitle, interestedCreatorDescription } = pageLabels;
  const { isError = false, isValidationError = [] } = state as {
    isError: boolean;
    isValidationError: ValidationError[];
  };
  const [creatorTypes, setCreatorsTypes] = useState<CreatorType[]>([]);
  const metadataService = new MetadataService(configuration.metadataClient);

  useEffect(() => {
    async function fetchData() {
      const creatorTypes = await getCreatorsTypes(
        metadataService,
        stableDispatch,
        INTERESTED_CREATOR_REAPPLY_PERIOD,
        interestedCreator as InterestedCreator,
        errorHandling
      );
      if (creatorTypes) setCreatorsTypes(creatorTypes as Array<CreatorType>);
    }
    fetchData();
  }, [stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(<Toast header={unhandledError} content={isError ? isError : toastContent(isValidationError)} />, {
        onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
      });
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);

  const handleModalClose = useCallback(() => setShowConfirmation(false), [setShowConfirmation]);

  const modalLabels = {
    title: pageLabels.modalConfirmationTitle,
    yes: formLabels.yes,
    no: formLabels.no,
    close: formLabels.close,
    confirmationDesc1: pageLabels.confirmationDesc1,
    confirmationDesc2: pageLabels.confirmationDesc2
  };

  return (
    <div className="mg-ic-ui-page-interested-creator">
      <section className="interested-creators-ui-creator-type-container">
        <div className="interested-creators-ui-intro">
          <h3 className="interested-creators-ui-creator-type-title">{interestedCreatorTitle}</h3>
        </div>
        <div className="interested-creators-ui-creator-type-description">{interestedCreatorDescription}</div>
        <InterestedCreatorsCreatorTypeForm
          formLabels={formLabels}
          interestedCreator={interestedCreator as InterestedCreator}
          router={router}
          onClose={onClose}
          creatorTypes={creatorTypes}
          analytics={analytics}
          stableDispatch={stableDispatch}
          state={state}
          configuration={configuration}
          errorHandling={errorHandling}
          redirectedToNextStepUrl={redirectedToNextStepUrl}
          basePath={basePath}
        />
        {showConfirmation && (
          <CancelRegistrationModal
            handleModalClose={handleModalClose}
            handleCancelRegistration={handleCancelRegistration}
            labels={modalLabels}
          />
        )}
      </section>
    </div>
  );
});
