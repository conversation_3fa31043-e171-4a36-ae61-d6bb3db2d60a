const coreUiKit = require("@eait-playerexp-cn/core-ui-kit/config");

module.exports = coreUiKit({
  content: ["src/**/*.{ts,tsx}"],
  theme: {
    extend: {
      fill: (theme) => ({
        current: "currentColor",
        "gray-10": theme("colors.gray.10"),
        "gray-50": theme("colors.gray.50"),
        "primary-50": theme("colors.primary.50"),
        "navy-60": theme("colors.navy.60"),
        "navy-80": theme("colors.navy.80"),
        "success-30": theme("colors.success.30"),
        "success-70": theme("colors.success.70")
      }),
      backgroundImage: {
        "migration-shape": "var(--migration-background)",
        "migration-web": "linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "migration-default":
          "linear-gradient(186.57deg, rgba(255, 71, 71, 0.77) -3.69%, rgba(12, 15, 64, 0.77) 12.16%), linear-gradient(351.37deg, #0D1042 74.35%, #FF4747 113.69%)",
        "interested-creator-layout": "var(--interested-creator-layout-background)",
        "interested-creator-bg-shape": "var(--interested-creator-shape-background)",
        "down-arrow-icon-background": "var(--down-arrow-icon-background)",
        "interested-creator-layout-default": "linear-gradient(23.91deg, #0D1042 69.51%, #FF4747 149.49%)"
      },
      boxShadow: {
        sm: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
        "gray-10-outline-10-opacity-0.7": "0px 0px 10px rgba(255, 255, 255, 0.7)",
        "gray-90-outline-4-8-apacity-0.15": " 0px 4px 8px 0px rgba(0, 0, 0, 0.15)"
      }
    },
    stroke: (theme) => ({
      "navy-40": theme("colors.navy.40"),
      "navy-60": theme("colors.navy.60"),
      "navy-80": theme("colors.navy.80"),
      "success-50": theme("colors.success.50"),
      "success-70": theme("colors.success.70")
    })
  },
  plugins: [require("@tailwindcss/typography")]
});
