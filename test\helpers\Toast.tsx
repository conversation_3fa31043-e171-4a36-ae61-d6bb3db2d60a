import { withToastProvider } from "@eait-playerexp-cn/core-ui-kit";
import { act, fireEvent, render, RenderResult } from "@testing-library/react";
import { ReactNode } from "react";
import React from "react";

export const triggerAnimationEnd = (node: Node[] | Node): void => {
  jest.spyOn(window, "requestAnimationFrame").mockImplementation((callback) => {
    callback(1);
    return 1;
  });

  act(() => {
    jest.runAllTimers();
    if (Array.isArray(node)) {
      node.forEach((el) => {
        fireEvent.animationEnd(el?.parentNode as Node);
      });
    } else {
      fireEvent.animationEnd(node.parentNode as Node);
    }
    jest.runAllTimers();
  });

  (window.requestAnimationFrame as jest.Mock).mockRestore();
};

export const renderWithToast = (component: ReactNode): RenderResult => {
  const TestWrapper = withToastProvider(() => component);

  return render(<TestWrapper {...{}} />);
};
