import { faker } from "@faker-js/faker";

const { image, string, location, helpers, internet, person, number } = faker;

export default class Random {
  static imageUrl(): string {
    return image.urlLoremFlickr();
  }

  static string(length: number = 8): string {
    return string.alphanumeric(length);
  }

  static country(): string {
    return location.country();
  }

  static countryCode(): string {
    return location.countryCode();
  }

  static creatorType(): string {
    return helpers.arrayElement(["YOUTUBER", "LIVE_STREAMER", "PODCASTER", "BLOGGER"]);
  }

  static franchiseType(): string {
    return helpers.arrayElement(["PRIMARY", "SECONDARY"]);
  }

  static franchise(): string {
    return helpers.arrayElement(["Need for Speed", "Apex Legends", "Madden NFL", "The Sims", "FIFA"]);
  }

  static uuid(): string {
    return string.uuid();
  }

  static locale(): string {
    return helpers.arrayElement(["en_US", "fr_CA", "es_ES", "de_<PERSON>", "it_IT", "ja_<PERSON>", "ko_K<PERSON>", "pt_BR", "zh_CN"]);
  }

  static url(): string {
    return image.url();
  }

  static email(): string {
    return internet.email();
  }

  static firstName(): string {
    return person.firstName();
  }

  static lastName(): string {
    return person.lastName();
  }

  static nucleusId(): number {
    return number.int();
  }

  static number(min?: number, max?: number): number {
    return number.int({ min, max });
  }
}
