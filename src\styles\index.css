@import "../Search/Search.css";
@import "./vars.css";
@import "../cards/Card/Card.css";
@import "../CancelRegistrationModal/CancelRegistrationModal.css";
@import "../RequestsToJoinStatusTable/RequestsToJoinStatusTable.css";
@import "../ExplorePage/ExplorePage.css";
@import "../AgeRestrictionPage/AgeRestrictionPage.css";
@import "../NoAccountPage/NoAccountPage.css";
@import "../ApplicationAcceptedPage/ApplicationAcceptedPage.css";
@import "../ApplicationCompletedPage/ApplicationCompletedPage.css";
@import "../ApplicationPendingPage/ApplicationPendingPage.css";
@import "../ApplicationRejectedPage/ApplicationRejectedPage.css";
@import "../InterestedCreatorsStartPage/InterestedCreatorsStartPage.css";
@import "../information/Information.css";
@import "../information//AdditionalContentAndWebsiteLinks/AdditionalContentAndWebsiteLinks.css";
@import "../information/InterestedCreatorInformationForm/InterestedCreatorInformationForm.css";
@import "../information/InterestedCreatorsInformationPage/InterestedCreatorsInformationPage.css";
@import "../Footer/Footer.css";
@import "../franchisesYouPlay/FranchisesYouPlay.css";
@import "../franchisesYouPlay/InterestedCreatorsFranchisesYouPlayPage/InterestedCreatorsFranchisesYouPlayPage.css";
@import "../franchisesYouPlay/InterestedCreatorsFranchisesYouPlayForm/InterestedCreatorsFranchisesYouPlayForm.css";
@import "../creatorType/CreatorType.css";
@import "../creatorType/InterestedCreatorsCreatorTypePage/InterestedCreatorsCreatorTypePage.css";
@import "../creatorType//InterestedCreatorsCreatorTypeForm/InterestedCreatorsCreatorTypeForm.css";
@import "../information/ConnectAccounts/ConnectAccountsForm/ConnectAccountsForm.css";
@import "../information/ConnectAccounts/ConnectAccountsForm/ConnectFacebookPagesModal.css";
@import "../information/ConnectAccounts/ConnectAccountsInputs/ConnectAccountsInputs.css";

input:disabled:-webkit-autofill,
input:disabled:-webkit-autofill:hover,
input:disabled:-webkit-autofill:focus,
input:disabled:-webkit-autofill:active {
  transition: background-color 5000s;
  -webkit-text-fill-color: #fff !important;
  -webkit-background-clip: text;
  color: white !important;
}
.mg-ic-ui-page-interested-creator {
  @apply flex h-full min-h-screen w-full flex-col items-center text-gray-10;
}

.interested-creators-ui .mg-ic-ui-page-interested-creator {
  min-height: -webkit-fill-available;
  min-height: -moz-available;
  min-height: stretch;
}
