import React, { memo } from "react";
import { ConnectedAccountComponent } from "../ConnectedAccount/ConnectedAccounts";
import ConnectAccountsInputs from "../ConnectAccountsInputs/ConnectAccountsInputs";
import { ConnectedAccount, Dispatch, ErrorHandling, State } from "../../../utils";
import { ConnectAccountLabels } from "../../InterestedCreatorsInformationPage/InterestedCreatorsInformationPage";
import { Configuration, ConnectAccounts } from "../../Information";

export type ConnectAccountsFormProps = {
  labels: ConnectAccountLabels;
  setAccountToRemove: (data: string) => void;
  setShowAddConfirmation: (data: boolean) => void;
  accountToRemove: string;
  showAddConfirmation: boolean;
  accounts: Array<ConnectedAccount>;
  setShowRemoveAccountModal: (data: boolean) => void;
  showRemoveAccountModal: boolean;
  state: State;
  errorHandling: ErrorHandling;
  stableDispatch: Dispatch;
  configuration: Configuration;
  connectAccounts: ConnectAccounts;
};
export const ConnectAccountsForm = memo(function ConnectAccountsForm({
  labels,
  setAccountToRemove,
  setShowAddConfirmation,
  accountToRemove,
  showAddConfirmation,
  accounts,
  setShowRemoveAccountModal,
  showRemoveAccountModal,
  state,
  errorHandling,
  stableDispatch,
  configuration,
  connectAccounts
}: ConnectAccountsFormProps) {
  return (
    <div className="ic-ui-connect-accounts-form">
      <div className="ic-ui-connect-accounts">
        <ConnectedAccountComponent
          {...{
            labels,
            setAccountToRemove,
            accountToRemove,
            accounts,
            setShowAddConfirmation,
            showAddConfirmation,
            setShowRemoveAccountModal,
            showRemoveAccountModal,
            state,
            errorHandling,
            stableDispatch,
            configuration,
            connectAccounts
          }}
        />
        <ConnectAccountsInputs
          {...{
            labels,
            setShowAddConfirmation,
            showAddConfirmation,
            errorHandling,
            stableDispatch,
            configuration,
            connectAccounts
          }}
        />
      </div>
    </div>
  );
});
