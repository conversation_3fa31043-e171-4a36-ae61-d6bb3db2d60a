import { <PERSON>a, <PERSON>Obj } from "@storybook/react";
import RequestsToJoinStatusTable from "./RequestsToJoinStatusTable";
import Rejected from "../styles/img/icons/Rejected";
import Unreviewed from "../styles/img/icons/Unreviewed";
import React, { ComponentType, ReactElement } from "react";

const meta: Meta<typeof RequestsToJoinStatusTable> = {
  title: "Component Library/Requests To Join Status Table",
  component: RequestsToJoinStatusTable,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof RequestsToJoinStatusTable>;

export const RejectedStatus: Story = {
  args: {
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    submittedDate: "Jan 02, 2024",
    emailId: "<EMAIL>",
    applicationStatus: "Rejected",
    statusIcon: Rejected,
    requestsToJoinStatusTableLabels: {
      programLabel: "Program",
      programName: "Support a Creator",
      email: "Email",
      status: "Status",
      submissionDate: "Submission Date"
    }
  }
};

export const PendingApprovalStatus: Story = {
  args: {
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    submittedDate: "Jan 02, 2024",
    emailId: "<EMAIL>",
    applicationStatus: "Pending Approval",
    statusIcon: Unreviewed,
    requestsToJoinStatusTableLabels: {
      programLabel: "Program",
      programName: "Support a Creator",
      email: "Email",
      status: "Status",
      submissionDate: "Submission Date"
    }
  }
};
