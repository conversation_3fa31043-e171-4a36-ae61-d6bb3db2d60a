import React, { FC, memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useMemo } from "react";
import { NextRouter } from "next/router";
import { Controller, FieldValues, useFormContext } from "react-hook-form";
import { BrowserAnalytics, COMPLETED_ONBOARDING_STEPS, Dispatch, ErrorHandling, State, useAsync } from "../../utils";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { FormLabels, InterestedCreatorsCreatorType } from "../CreatorType";
import CheckboxCards from "../../cards/CheckboxCards/CheckboxCards";
import Footer from "../../Footer/Footer";
import InterestedCreatorsService, { InterestedCreator } from "../../Browser/InterestedCreatorsService";
import Form from "../../utils/Form";
import { Configuration } from "../../information/Information";
import { CreatorType } from "@eait-playerexp-cn/metadata-types";

export type CreatorTypeProps = {
  creatorsType: CreatorType[];
  values: string[];
  readOnly: boolean;
  formLabels: FormLabels & InterestedCreatorsCreatorTypeFormLabels;
  onClose: MouseEventHandler<HTMLButtonElement>;
  isPending: boolean;
  basePath?: string;
};

export type CreatorsTypeLabels = {
  label: string;
  value: string;
};
const InterestedCreatorsCreatorTypeInput = memo(function InterestedCreatorsCreatorTypeInput({
  creatorsType,
  values = [],
  formLabels,
  onClose,
  isPending,
  basePath
}: CreatorTypeProps) {
  const methods = useFormContext();
  const { control } = methods;
  const { requiredMessage, creatorsTypeLabels } = formLabels;
  const creatorTypesProps = useMemo(
    () => ({
      name: "creatorTypes",
      requiredMessage: requiredMessage,
      values: creatorsType.filter((item) => values.indexOf(item.value) !== -1),
      items: creatorsType.map((item) => {
        const label =
          creatorsTypeLabels.find((creatorTypelabel) => creatorTypelabel.value === item.value)?.label || item.label;
        return {
          ...item,
          label: label,
          checked: values.indexOf(item.value) !== -1
        };
      })
    }),
    [creatorsType, values]
  );

  const buttons = useMemo(() => {
    const { cancel, next } = formLabels;
    return { cancel, next };
  }, [formLabels]);

  const { formState } = useFormContext();
  const disableSubmit = Object.keys(formState.errors).length !== 0 || formState.isValid === false || isPending || false;

  return (
    <>
      <div className="creator-type-card-container">
        <Controller
          control={control}
          name={creatorTypesProps.name}
          defaultValue={creatorTypesProps.values}
          rules={{ required: creatorTypesProps.requiredMessage }}
          render={({ field, fieldState: { error } }) => (
            <CheckboxCards
              readOnly={undefined}
              selectAlternateItem={undefined}
              {...field}
              errorMessage={(error && error.message) || ""}
              items={creatorTypesProps.items}
              disabled={false}
              basePath={basePath}
            />
          )}
        />
      </div>
      <Footer buttons={buttons} disableSubmit={disableSubmit} onCancel={onClose} />
    </>
  );
});

export type InterestedCreatorsCreatorTypeFormLabels = {
  cancel: string;
  next: string;
  requiredMessage: string;
  creatorsTypeLabels: Array<CreatorsTypeLabels>;
};

export type NavStep = {
  icon: FC<SvgProps>;
  title: string;
  href: string;
  isCompleted?: boolean;
};

export type InterestedCreatorsCreatorTypeFormProps = {
  creatorTypes: CreatorType[];
  onClose: MouseEventHandler<HTMLButtonElement>;
  formLabels: FormLabels & InterestedCreatorsCreatorTypeFormLabels;
  interestedCreator: InterestedCreatorsCreatorType;
  router: NextRouter;
  analytics: BrowserAnalytics;
  stableDispatch: Dispatch;
  state: State;
  configuration: Configuration;
  errorHandling: ErrorHandling;
  redirectedToNextStepUrl: string;
  basePath?: string;
};

export default memo(function InterestedCreatorsCreatorTypeForm({
  creatorTypes = [],
  formLabels,
  interestedCreator = { creatorTypes: [] } as InterestedCreatorsCreatorType,
  router,
  onClose,
  analytics,
  stableDispatch,
  state,
  configuration,
  errorHandling,
  redirectedToNextStepUrl,
  basePath
}: InterestedCreatorsCreatorTypeFormProps) {
  const { onboardingSteps } = state as { onboardingSteps: Array<NavStep> };
  const interestedCreatorsService = new InterestedCreatorsService(configuration.applicationsClient);
  const onSubmit = useCallback(
    async (data: FieldValues) => {
      const currentStep = onboardingSteps.find((step) => step.href === router.pathname);
      try {
        await interestedCreatorsService.saveApplication({ ...(data as InterestedCreator) });
      } catch (e) {
        errorHandling(stableDispatch, e as unknown as Error);
        return;
      }
      if (analytics.continuedCreatorApplication) {
        analytics.continuedCreatorApplication({
          locale: router.locale ?? "",
          page: location.pathname,
          finalStep: false,
          creatorTypes: data.creatorTypes?.map((type: { value: string }) => type.value)
        });
      }
      stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });

      router.push(redirectedToNextStepUrl);
    },
    [router]
  );

  const values = useMemo(() => {
    return (
      interestedCreator.creatorTypes && interestedCreator.creatorTypes.map((key) => (key as { value: string }).value)
    );
  }, [interestedCreator?.creatorTypes]);

  const { pending: isPending, execute: submitHandler } = useAsync(onSubmit, false);

  return (
    !!creatorTypes.length && (
      <Form mode="onChange" key="creatorType" onSubmit={submitHandler} defaultValues={{}}>
        <InterestedCreatorsCreatorTypeInput
          creatorsType={creatorTypes}
          values={values ?? []}
          readOnly={false}
          formLabels={formLabels}
          onClose={onClose}
          isPending={isPending}
          basePath={basePath}
        />
      </Form>
    )
  );
});
