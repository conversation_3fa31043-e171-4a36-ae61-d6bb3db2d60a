import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import React from "react";
import { DOMAIN_ERROR, ERROR, VALIDATION_ERROR, ValidationError } from "./index";

const SUCCESS = "SUCCESS";

export function toastContent(validationErrors: ValidationError[]): JSX.Element {
  return (
    <ul className="formatted-content">
      {validationErrors.map((error, index) =>
        !Array.isArray(error.errorMessages) ? (
          <li className="text-gray-90" key={index}>
            {`${error.propertyName} ${error.errorMessages}`}
          </li>
        ) : (
          <li className="text-gray-90" key={index}>
            {error.errorMessages.length === 1 ? (
              `${error.propertyName} ${error.errorMessages[0]}`
            ) : (
              <>
                {error.propertyName}
                <ul key={"messages-" + index}>
                  {error.errorMessages.map((message, messageIndex) => (
                    <li key={"message-" + messageIndex}>{message}</li>
                  ))}
                </ul>
              </>
            )}
          </li>
        )
      )}
    </ul>
  );
}

export const onToastClose = (toast: string, dispatch: (action: { type: string; data: boolean }) => void): void => {
  if (toast === ERROR) {
    dispatch({ type: ERROR, data: false });
  }
  if (toast === SUCCESS) {
    dispatch({ type: SUCCESS, data: false });
  }
  if (toast === VALIDATION_ERROR) {
    dispatch({ type: VALIDATION_ERROR, data: false });
  }
  if (toast === DOMAIN_ERROR) {
    dispatch({ type: DOMAIN_ERROR, data: false });
  }
};

interface ErrorResponse {
  response?: {
    data?: {
      code?: string;
      message?: string;
      detail?: string;
    };
  };
  code?: string;
}

type ErrorMap = Map<string, string>;

export const getExtractedErrorMessage = (
  errorMap: ErrorMap,
  error: ErrorResponse | boolean,
  fallback: string
): string => {
  return (
    errorMap.get(((error as ErrorResponse)?.response?.data?.code || (error as ErrorResponse)?.code) ?? "") ||
    (error as ErrorResponse)?.response?.data?.message ||
    (error as ErrorResponse)?.response?.data?.detail ||
    fallback
  );
};
// ---------------------------------
// Age must be 18 years or older
// ---------------------------------
export function isAdult(birthDate: string): boolean {
  const dateOfBirth = LocalizedDate.fromFormattedDate(birthDate);
  return dateOfBirth.isAfter(LocalizedDate.subtractFromNow(18, "years"));
}

export function isObj(obj: object): boolean {
  return Object.prototype.toString.call(obj) === "[object Object]";
}

export function isString(obj: object): boolean {
  return Object.prototype.toString.call(obj) === "[object String]";
}

export const isEmpty = (n: [] | object): boolean => {
  return !(!!n ? (typeof n === "object" ? (Array.isArray(n) ? !!n.length : !!Object.keys(n).length) : true) : false);
};
