.request-to-join-status-table {
  @apply mb-meas16 w-[290px] md:w-[630px] py-meas7 text-white border-collapse border border-opacity-[0.33];
  background: rgba(12, 14, 49, 0.85);
}
@media screen and (max-width: 767px) {
  .request-to-join-status-table-header {
    @apply hidden;
  }
  .request-to-join-status-table-body {
    @apply flex flex-col items-start py-meas16 md:py-meas0 pl-[20px] pr-[10px] gap-[24px];
  }
  .request-to-join-status-table-row {
    @apply flex justify-start;
  }
  .request-to-join-status-table {
    @apply mx-auto;
  }
}
.request-to-join-status-table-divider {
  @apply mx-auto max-w-[630px] opacity-[0.33];
}
.request-to-join-status-table-header {
  @apply max-w-[630px] h-[61px] border-b-[1px] border-[rgba(255,255,255,0.33)];
}
.request-to-join-status-table-body {
  @apply md:h-[61px];
  background: #272950;
}
.request-to-join-status-table-col {
  @apply min-w-[125px] pl-meas10 text-left font-text-bold xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1;
}
.request-to-join-status-table-row {
  @apply min-w-[125px] break-all text-left font-text-regular xs:text-desktop-body-small md:text-desktop-caption1 md:pl-meas10;
}
.request-to-join-status-container {
  @apply flex justify-center md:justify-start md:items-center;
}
.request-to-join-status-table-emailId {
  @apply min-w-[170px];
}
.request-to-join-status-table-row.request-to-join-status-table-program {
  @apply xs:font-text-bold md:font-text-regular;
}
.request-to-join-status-icon {
  @apply my-auto mr-meas4 h-meas10 w-meas10;
}
