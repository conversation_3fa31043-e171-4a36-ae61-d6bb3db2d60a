import React, { FC, useEffect } from "react";
import { BrowserAnalytics } from "../utils/types";
import { Button } from "@eait-playerexp-cn/core-ui-kit";

export type Labels = {
  title: string;
  descriptionPara1: string;
  descriptionPara2: string;
  completeYourProfile: string;
  creatorNetwork: string;
  close: string;
  applicationAcceptedThumbnailAltText: string;
};
export type ApplicationAcceptedPageProps = {
  labels: Labels;
  locale: string;
  analytics: BrowserAnalytics;
  applicationAcceptedThumbnail: string;
  onButtonClick: () => void;
};

const ApplicationAcceptedPage: FC<ApplicationAcceptedPageProps> = ({
  labels,
  locale,
  analytics,
  applicationAcceptedThumbnail,
  onButtonClick
}) => {
  const { title, descriptionPara1, descriptionPara2, completeYourProfile, applicationAcceptedThumbnailAltText } =
    labels;
  useEffect(() => {
    if (analytics.checkedApplicationStatus) {
      analytics.checkedApplicationStatus({ locale, status: "Accepted" });
    }
  }, [locale]);

  return (
    <div className="mg-ic-ui-page-interested-creator">
      <div className="application-accepted-wrapper">
        <img
          src={applicationAcceptedThumbnail}
          alt={applicationAcceptedThumbnailAltText}
          className="application-accepted-thumbnail"
        />
        <div className="application-accepted-content-container">
          <h3 className="application-accepted-title">{title}</h3>
          <div className="application-accepted-descriptionPara1">{descriptionPara1}</div>
          <div className="application-accepted-descriptionPara2">{descriptionPara2}</div>
        </div>
        <div className="application-accepted-complete-profile">
          <Button onClick={onButtonClick} type="button" variant="primary" size="md">
            {completeYourProfile}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ApplicationAcceptedPage;
