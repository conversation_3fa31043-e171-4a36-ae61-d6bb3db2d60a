export const interestedCreatorInformationFormLabels = {
  formLabels: {
    infoTitle: "Your information",
    firstName: "First Name",
    lastName: "Last Name",
    eaEmailID: "EA Email ID",
    creatorEmail: "<EMAIL>",
    dateOfBirth: "Date of Birth",
    preferredEmail: "Preferred Email for Communication",
    country: "Country/Region",
    contentMediaTitle: "Content Media",
    contentMediaDescription: "Please provide links to your content media accounts.",
    contentUrlPlaceholder: "Content URL",
    contentUrl: "Content URL",
    contentFollowers: "Followers",
    contentFollowersPlaceholder: "Followers",
    addAnother: "Add Another",
    contentLanguagesTitle: "Content Languages",
    contentLanguagesDescription: "This is the language(s) in which you produce content in.",
    contentLanguage: "Channel Language(s)",
    languageTitle: "Language for Communication",
    languageDescription: "This is the communication language that EA will use with you.",
    language: "Language",
    cancel: "Cancel",
    next: "Next",
    duplicateUrl: "Duplicate URLs not allowed",
    urlScanFailed: "You cannot submit content from this website/domain at this time.",
    followersMaxLength: "followersMaxLength",
    selectCountry: "Select Country",
    remove: "Remove",
    ok: "OK",
    calendar: "calendar",
    close: "Close",
    connectSocialMediaAccountTitle: "Connect Social Media Account",
    connectSocialMediaAccountDescription:
      "Please connect at least one of your social media accounts with your content. You may also  connect multiple accounts on each social media (Not including Instagram). This will help our team assess our compatibility.  For security reasons, please connect your accounts again.",
    additionalContentAndWebsiteTitle: "Additional Content and Website Links",
    additionalContentAndWebsiteDescription:
      "If you want to share specific content and other website links to be reviewed by our team you can add up to 10 links below!",
    websiteUrlLabel: "Website URL(Optional)",
    additionalLinkPlaceholder: "Example: https://www.mywebsite.com/mylink",
    addMoreUrlLabel: "Add another URL",
    invalidUrl: "invalidUrl",
    ageMustBe18OrOlder: "Must be 18 or older"
  }
};
