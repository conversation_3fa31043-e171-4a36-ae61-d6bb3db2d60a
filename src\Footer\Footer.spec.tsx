import React from "react";
import { render, screen } from "@testing-library/react";
import Footer from "./Footer";
import { axe } from "jest-axe";

describe("Footer", () => {
  const footerInput = {
    buttons: { cancel: "Cancel", next: "Next" },
    onCancel: jest.fn(),
    disableSubmit: true,
    isPending: true,
    isFranchisesYouPlayPage: false
  };

  beforeEach(() => jest.clearAllMocks());

  it("includes two buttons: Cancel and Next", async () => {
    render(<Footer {...footerInput} />);

    expect(await screen.findByText(/Cancel/i)).toBeInTheDocument();
    expect(await screen.findByText(/Next/i)).toBeInTheDocument();
  });

  it("changes 'next' button label to Submit", async () => {
    render(<Footer {...{ ...footerInput, buttons: { cancel: "Cancel", next: "Submit" } }} />);

    expect(await screen.findByText(/Cancel/i)).toBeInTheDocument();
    expect(await screen.findByText(/Submit/)).toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(<Footer {...footerInput} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
