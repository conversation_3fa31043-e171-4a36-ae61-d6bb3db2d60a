import { Icon, SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import React, { FC, forwardRef, useCallback, useEffect, useState } from "react";

export type Item = {
  label?: string;
  image?: string;
  checked: boolean;
  icon?: FC<SvgProps>;
  imageAsIcon?: string;
};

export type CheckboxCardProps = {
  disabled?: boolean;
  onChange: (item: Item, isChecked: boolean) => void;
  item?: Item;
  readOnly?: boolean;
  key: number;
  basePath?: string;
};

const CheckboxCard = forwardRef<HTMLInputElement, CheckboxCardProps>(
  ({ disabled, onChange, item, readOnly, basePath }, ref) => {
    const { label, image, checked, icon, imageAsIcon } = item ?? {};
    const styles = image ? { backgroundImage: `url(${image})` } : {};
    const [selectedClass, setSelectedClass] = useState("");

    useEffect(() => {
      if (!disabled) {
        setSelectedClass(checked ? " selected-card" : "");
      }
    }, [disabled, checked]);

    const cardOnClick = useCallback(
      (isChecked: boolean) => {
        setSelectedClass(isChecked ? " selected-card" : "");
        if (onChange) {
          onChange(item ?? { label: "", checked: true }, isChecked);
        }
      },
      [onChange, item]
    );

    return (
      <div
        className="ic-ui-card-col"
        data-disabled={disabled}
        onChange={(e) => !readOnly && cardOnClick((e.target as HTMLInputElement).checked)}
      >
        <label className={`ic-ui-check-container${selectedClass}`}>
          <input
            ref={ref}
            data-testid={label}
            aria-label={label}
            type="checkbox"
            defaultChecked={checked}
            disabled={disabled}
          />
          <span className={`ic-ui-checkmark-layout${selectedClass}`}>&nbsp;</span>
          <span className="ic-ui-checkmark" style={styles} data-disabled={disabled}>
            {icon && <Icon icon={icon} />}
            {imageAsIcon && <img alt="" src={`${basePath || ""}${imageAsIcon}`} className="image-as-icon" />}
            <svg
              width="64"
              height="64"
              viewBox="0 0 64 64"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="ic-ui-checkmark-box"
            >
              <path d="M64 64L0 0H64V64Z" fill="currentFillColor" />
              <circle cx="46" cy="19" r="13.25" fill="currentColor" stroke="currentStrokeColor" strokeWidth="1.5" />
            </svg>
          </span>
        </label>
        <div className="checkbox-title">
          <span className="card-title">{label}</span>
        </div>
      </div>
    );
  }
);

export default CheckboxCard;
