import React from "react";
import { BrowserAnalytics } from "../../utils";
import { render, screen, waitFor } from "@testing-library/react";
import "next/config";
import { NextRouter } from "next/router";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import userEvent from "@testing-library/user-event";
import { aCreatorType } from "@eait-playerexp-cn/metadata-test-fixtures";
import InterestedCreatorsCreatorTypeForm, {
  InterestedCreatorsCreatorTypeFormProps
} from "./InterestedCreatorsCreatorTypeForm";
import { interestedCreatorsCreatorTypeFormLabels } from "../../../test/translations/InterestedCreatorsCreatorTypeForm";
import InterestedCreatorsService from "../../Browser/InterestedCreatorsService";
import { Configuration } from "../../information/Information";
import { CreatorType } from "@eait-playerexp-cn/metadata-types";

jest.mock("../../Browser/InterestedCreatorsService");

describe("InterestedCreatorsCreatorTypeForm", () => {
  const locale = "en-us";
  const configuration = { metadataClient: {}, applicationsClient: {} } as Configuration;
  const creatorTypes = [
    aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
    aCreatorType({ label: "lifestyle", value: "LIFESTYLE" }),
    aCreatorType({ label: "photographer", value: "PHOTOGRAPHER" }),
    aCreatorType({ label: "designer_artist", value: "DESIGNER_ARTIST" }),
    aCreatorType({ label: "blogger", value: "BLOGGER" }),
    aCreatorType({ label: "live_streamer", value: "LIVE_STREAMER" }),
    aCreatorType({ label: "podcaster", value: "PODCASTER" }),
    aCreatorType({ label: "cosplayer", value: "COSPLAYER" }),
    aCreatorType({ label: "animator", value: "ANIMATOR" }),
    aCreatorType({ label: "screenshoter", value: "SCREENSHOTER" }),
    aCreatorType({ label: "other", value: "OTHER" })
  ];
  const analytics = {} as unknown as BrowserAnalytics;
  const onClose = jest.fn();
  const interestedCreator = {
    nucleusId: 0,
    defaultGamerTag: "",
    originEmail: "",
    dateOfBirth: undefined,
    contentUrls: [{ url: "", followers: "" }],
    creatorTypes: []
  };
  const router = { locale, push: jest.fn() } as unknown as NextRouter;
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];

  const interestedCreatorsCreatorTypeFormProps: InterestedCreatorsCreatorTypeFormProps = {
    creatorTypes: creatorTypes as unknown as CreatorType[],
    onClose,
    formLabels: interestedCreatorsCreatorTypeFormLabels,
    interestedCreator,
    router,
    analytics,
    stableDispatch: jest.fn(),
    state: {
      onboardingSteps: steps
    },
    configuration,
    errorHandling: jest.fn(),
    redirectedToNextStepUrl: "/interested-creators/franchises-you-play",
    basePath: "/support-a-creator"
  };

  beforeEach(() => {
    jest.resetAllMocks();
  });

  it("shows all existing creator types", async () => {
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} />);

    await waitFor(() => {
      expect(screen.getAllByRole("checkbox")).toHaveLength(creatorTypes.length);
      expect(screen.getByTestId(/youtuber/i)).not.toBeChecked();
      expect(screen.getByTestId(/lifestyle/i)).not.toBeChecked();
      expect(screen.getByTestId(/photographer/i)).not.toBeChecked();
      expect(screen.getByTestId("Designer/Artist")).not.toBeChecked();
      expect(screen.getByTestId(/blogger/i)).not.toBeChecked();
      expect(screen.getByTestId(/live streamer/i)).not.toBeChecked();
      expect(screen.getByTestId(/podcaster/i)).not.toBeChecked();
      expect(screen.getByTestId(/cosplayer/i)).not.toBeChecked();
      expect(screen.getByTestId(/animator/i)).not.toBeChecked();
      expect(screen.getByTestId(/screenshoter/i)).not.toBeChecked();
      expect(screen.getByTestId(/other/i)).not.toBeChecked();
    });
  });

  it("pre-populates checkboxes values", async () => {
    const interestedCreatorWithTypes = {
      ...interestedCreator,
      creatorTypes: [
        aCreatorType({ label: "youtuber", value: "YOUTUBER" }),
        aCreatorType({ label: "lifestyle", value: "LIFESTYLE" })
      ]
    };
    render(
      <InterestedCreatorsCreatorTypeForm
        {...interestedCreatorsCreatorTypeFormProps}
        interestedCreator={interestedCreatorWithTypes}
      />
    );

    await waitFor(() => {
      expect(screen.getAllByRole("checkbox")).toHaveLength(creatorTypes.length);
      expect(screen.getByTestId(/youtuber/i)).toBeChecked();
      expect(screen.getByTestId(/lifestyle/i)).toBeChecked();
      expect(screen.getByTestId(/photographer/i)).not.toBeChecked();
      expect(screen.getByTestId("Designer/Artist")).not.toBeChecked();
      expect(screen.getByTestId(/blogger/i)).not.toBeChecked();
      expect(screen.getByTestId(/live streamer/i)).not.toBeChecked();
      expect(screen.getByTestId(/podcaster/i)).not.toBeChecked();
      expect(screen.getByTestId(/cosplayer/i)).not.toBeChecked();
      expect(screen.getByTestId(/animator/i)).not.toBeChecked();
      expect(screen.getByTestId(/screenshoter/i)).not.toBeChecked();
      expect(screen.getByTestId(/other/i)).not.toBeChecked();
    });
  });

  it("enables next button on populating required values", async () => {
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} />);
    const nextButton = screen.getByRole("button", { name: /next/i });
    await waitFor(() => expect(nextButton).toBeDisabled());

    await userEvent.click(screen.getByTestId(/youtuber/i));

    await waitFor(() => expect(nextButton).toBeEnabled());
  });

  it("display error message if no checkbox is selected", async () => {
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} />);

    const nextButton = screen.getByRole("button", { name: /next/i });
    const youtuber = screen.getByTestId(/youtuber/i);
    await waitFor(() => expect(nextButton).toBeDisabled());
    await userEvent.click(youtuber);
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(youtuber);

    await waitFor(() => {
      expect(nextButton).toBeDisabled();
      expect(screen.getByText(/This field is required$/i)).toBeInTheDocument();
    });
  });

  it("redirects to the next page in the application flow", async () => {
    const service = {
      saveApplication: jest.fn().mockImplementation(() => Promise.resolve())
    } as unknown as InterestedCreatorsService;
    (InterestedCreatorsService as jest.Mock).mockReturnValue(service);
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} analytics={analytics} />);
    const nextButton = screen.getByRole("button", { name: /next/i });
    await waitFor(() => expect(nextButton).toBeDisabled());
    await userEvent.click(screen.getByTestId(/youtuber/i));
    await waitFor(async () => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        creatorTypes: ["YOUTUBER"],
        finalStep: false,
        locale: locale,
        page: "/"
      });
      expect(router.push).toHaveBeenCalledWith("/interested-creators/franchises-you-play");
      expect(service.saveApplication).toHaveBeenCalledTimes(1);
    });
  });

  it("handles API error while submit creator types", async () => {
    const service = {
      saveApplication: jest.fn().mockRejectedValue({
        response: {
          data: {
            statusCode: 401,
            message: "Unauthorized"
          }
        }
      })
    } as unknown as InterestedCreatorsService;
    (InterestedCreatorsService as jest.Mock).mockReturnValue(service);
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    render(<InterestedCreatorsCreatorTypeForm {...interestedCreatorsCreatorTypeFormProps} analytics={analytics} />);
    const nextButton = screen.getByRole("button", { name: /next/i });
    await waitFor(() => expect(nextButton).toBeDisabled());
    await userEvent.click(screen.getByTestId(/youtuber/i));
    await waitFor(async () => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(service.saveApplication).toHaveBeenCalledTimes(1);
    });
  });
});
