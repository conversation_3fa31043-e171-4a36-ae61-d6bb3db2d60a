import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import ApplicationCompletedPage from "./ApplicationCompletedPage";

const meta: Meta<typeof ApplicationCompletedPage> = {
  title: "Component Library/Application Completed Component",
  component: ApplicationCompletedPage,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ApplicationCompletedPage>;

export const ApplicationCompletedComponent: Story = {
  args: {
    labels: {
      close: "Close",
      creatorNetwork: "Creator Network",
      title: "Submission Complete",
      subTitle: "Thank you for submitting your request to join the EA Support a Creator Program",
      description:
        "Your submission has been sent for review by a member of the EA Creator Network team. If you are accepted, you’ll receive an email with an invitation to join!",
      backHome: "Back home",
      submissionCompleteSubTitle: "Still interested in joining?",
      submissionCompleteDescription:
        "Due to the high volume of submissions, your previous submission has not yet been reviewed. If you are still interested in joining EA Creator Network, please review and update your information to resubmit.",
      email: "Email",
      programLabel: "Program",
      programName: "Support a Creator",
      status: "Status",
      submissionDate: "Submission Date",
      unReviewed: "Unreviewed"
    },
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    emailId: "<EMAIL>",
    onBackButtonClick: () => {},
    applicationCompleteThumbnail: "./img/Players-comp.png"
  }
};

export const ApplicationCompletedComponentWithUnreviewed: Story = {
  args: {
    labels: {
      close: "Close",
      creatorNetwork: "Creator Network",
      title: "Submission Complete",
      subTitle: "Thank you for submitting your request to join the EA Support a Creator Program",
      description:
        "Your submission has been sent for review by a member of the EA Support a Creator Team. If you are not accepted, you’ll recieve an email with an invitation to join!",
      backHome: "Back Home",
      submissionCompleteSubTitle: "Still interested in joining?",
      submissionCompleteDescription:
        "Due to the high volume of submissions, your previous submission has not yet been reviewed. If you are still interested in joining EA Creator Network, please review and update your information to resubmit.",
      email: "Email",
      programLabel: "Program",
      status: "Status",
      submissionDate: "Submission Date",
      unReviewed: "Unreviewed",
      programName: "Support a Creator"
    },
    INTERESTED_CREATOR_REAPPLY_PERIOD: true,
    emailId: "<EMAIL>",
    submittedDate: "12/12/2012",
    onBackButtonClick: () => {},
    applicationCompleteThumbnail: "./img/Players-comp.png"
  }
};
