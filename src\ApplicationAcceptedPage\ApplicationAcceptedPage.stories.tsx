import React, { ComponentType, ReactElement } from "react";
import { Meta, StoryObj } from "@storybook/react";
import ApplicationAcceptedPage from "./ApplicationAcceptedPage";

const meta: Meta<typeof ApplicationAcceptedPage> = {
  title: "Component Library/Application Accepted Component",
  component: ApplicationAcceptedPage,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ApplicationAcceptedPage>;

export const ApplicationAcceptedComponent: Story = {
  args: {
    labels: {
      close: "Close",
      creatorNetwork: "Creator Network",
      title: "You’re in!",
      descriptionPara1:
        "Congratulations you have been accepted into our EA Support a Creator Program! You’ve demonstrated your ability to create engaging content.",
      descriptionPara2: "All you have to do now is complete your EA Support a Creator profile",
      completeYourProfile: "Complete your profile",
      applicationAcceptedThumbnailAltText: "Image not found"
    },
    locale: "en-us",
    analytics: { checkedApplicationStatus: () => {} },
    onButtonClick: () => {},
    applicationAcceptedThumbnail: "./img/Players-comp.png"
  }
};
