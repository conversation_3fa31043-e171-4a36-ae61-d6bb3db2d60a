.interested-creators-ui.mg-ic-ui-container {
  @apply pb-meas0;
}
.interested-creators-ui section {
  @apply w-[288px] md:w-[635px] mt-meas20;
}
.interested-creators-ui section > form {
  @apply w-full pt-meas16;
}
.interested-creators-ui-creator-type-container .ic-ui-card-container {
  @apply border-t border-[rgba(255,255,255,0.33)] pt-meas16;
}
@media screen and (min-width: 768px) {
  .interested-creators-ui .slider-content {
    transform: none !important; /*Unfortunately had to do this to override inline style*/
  }
}
.interested-creators-ui .mg-ic-ui-page-interested-creator {
  min-height: -webkit-fill-available;
  min-height: -moz-available;
  min-height: stretch;
}
.interested-creators-ui .mg-ic-ui-header-container {
  @apply flex-1;
}
.interested-creators-ui .mg-ic-ui-header-logo a {
  @apply flex items-center;
}
.interested-creators-ui .mg-ic-ui-header-close {
  @apply mr-meas7 mt-meas0;
}
.interested-creators-ui .mg-ic-ui-header {
  @apply items-center;
}
.interested-creators-ui {
  @apply px-meas0 pb-meas0;
}
.interested-creators-ui .mg-ic-ui-header-container {
  @apply px-meas8;
}
.interested-creators-ui .mg-ic-ui-page-interested-creator form {
  @apply w-[292px] md:w-[635px];
}
