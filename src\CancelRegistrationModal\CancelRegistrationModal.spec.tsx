import React from "react";
import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useRouter } from "next/router";
import CancelRegistrationModal from "./CancelRegistrationModal";
import { renderPage } from "../../test/helpers/Page";

jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));

describe("Cancel Registration Modal", () => {
  const cancelRegistrationModalProps = {
    labels: {
      title: "modalConfirmationTitle",
      yes: "yes",
      no: "no",
      close: "close",
      confirmationDesc1: "confirmationDesc1"
    },
    handleModalClose: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => ({ locale: "en-us" }));
  });

  it("shows cancel registration modal", () => {
    renderPage(<CancelRegistrationModal {...cancelRegistrationModalProps} />);

    expect(screen.getByRole("heading", { name: cancelRegistrationModalProps.labels.title })).toBeInTheDocument();
    expect(screen.getByText(cancelRegistrationModalProps.labels.confirmationDesc1)).toBeInTheDocument();
    expect(screen.getByRole("link", { name: cancelRegistrationModalProps.labels.yes })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: cancelRegistrationModalProps.labels.no })).toBeInTheDocument();
  });

  it("shows additional description", () => {
    const confirmationDesc2 = "confirmationDesc2";
    renderPage(
      <CancelRegistrationModal
        {...cancelRegistrationModalProps}
        labels={{ ...cancelRegistrationModalProps.labels, confirmationDesc2: confirmationDesc2 }}
      />
    );

    expect(screen.getByText(confirmationDesc2)).toBeInTheDocument();
    expect(screen.getByText(confirmationDesc2)).toHaveClass("cancel-registration-modal-confirmation-additional-desc");
  });

  it("executes cancel registration handler", async () => {
    const handleCancelRegistration = jest.fn();
    renderPage(
      <CancelRegistrationModal {...cancelRegistrationModalProps} handleCancelRegistration={handleCancelRegistration} />
    );
    await userEvent.click(screen.getByRole("button", { name: cancelRegistrationModalProps.labels.yes }));

    expect(handleCancelRegistration).toHaveBeenCalledTimes(1);
  });

  it("shows 'Yes' label as a link when cancel handler not provided", async () => {
    renderPage(<CancelRegistrationModal {...cancelRegistrationModalProps} />);

    const yesTextLink = screen.getByRole("link", { name: cancelRegistrationModalProps.labels.yes });
    expect(yesTextLink).toBeInTheDocument();
    expect(yesTextLink).toHaveAttribute("href", "/api/logout");
  });

  it("executes close handler", async () => {
    renderPage(<CancelRegistrationModal {...cancelRegistrationModalProps} />);

    await userEvent.click(screen.getByRole("button", { name: cancelRegistrationModalProps.labels.no }));

    expect(cancelRegistrationModalProps.handleModalClose).toHaveBeenCalledTimes(1);
  });
});
