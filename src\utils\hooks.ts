import { useCallback, useEffect, useRef, useState } from "react";
import { FieldValues } from "react-hook-form";

interface UseAsyncReturn<T, R> {
  execute: (data: T) => Promise<void>;
  pending: boolean;
  value: R | null;
  error: Error | undefined;
}

export const useAsync = <T extends FieldValues, R>(
  asyncFunction: (data: T) => Promise<R>,
  immediate = true
): UseAsyncReturn<T, R> => {
  const [pending, setPending] = useState(false);
  const [value, setValue] = useState<R | null>(null);
  const [error, setError] = useState<Error | undefined>();

  // The execute function wraps our function and
  // handles setting state for pending, value, and error.
  // useCallback ensures the below useEffect is not called
  // on every render, but only if asyncFunction changes.
  const execute = useCallback(
    (data: T) => {
      setPending(true);
      setValue(null);
      setError(undefined);
      return asyncFunction(data)
        .then((response: R) => setValue(response))
        .catch((error: Error) => setError(error))
        .finally(() => setPending(false));
    },
    [asyncFunction]
  );

  // Call execute if we want to fire it right away.
  // Otherwise execute can be called later, such as
  // in an onClick handler.
  useEffect(() => {
    if (immediate) {
      // execute();
    }
  }, [execute, immediate]);

  return { execute, pending, value, error };
};

export function useIsMounted(): () => boolean {
  const isMounted = useRef(false);

  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  return useCallback(() => isMounted.current, []);
}
