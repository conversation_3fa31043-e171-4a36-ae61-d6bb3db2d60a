import React from "react";
import { act, render, screen, waitFor } from "@testing-library/react";
import { useRouter } from "next/router";
import { axe } from "jest-axe";
import userEvent from "@testing-library/user-event";
import { informationLabels } from "../../../test/translations/Information";
import { interestedCreatorInformationFormLabels } from "../../../test/translations/InterestedCreatorInformationForm";
import InterestedCreatorsInformationPage, {
  InterestedCreatorsInformationPageProps
} from "./InterestedCreatorsInformationPage";
import { BrowserAnalytics } from "../../utils";
import CreatorForm from "../../utils/CreatorForm";
import { communicationPreferencesLabels } from "../../../test/translations/CommunicationPreferences";
import { aLocalizedDate } from "../../../test/factories/LocalizedDateBuilderFactories";
import { Configuration, Rules } from "../Information";
import { aCountry, aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";
import { renderPage } from "../../../test/helpers/Page";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";
import SubmittedContentService from "../../Browser/SubmittedContentService";
import InterestedCreatorsService from "../../Browser/InterestedCreatorsService";
import { ConnectAccountLabels } from "../../../test/translations/ConnectAccounts";
import ConnectedAccountsService from "../../Browser/ConnectedAccountsService";
import Random from "../../../test/factories/Random";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("../../Browser/InterestedCreatorsService");
jest.mock("../../Browser/SubmittedContentService");
jest.mock("../../Browser/ConnectedAccountsService");
jest.mock("next/router", () => ({
  useRouter: jest.fn(() => ({ locale: "en-us", push: jest.fn() }))
}));

describe("InterestedCreatorsInformationPage", () => {
  const labels = {
    FormLabels: interestedCreatorInformationFormLabels.formLabels,
    PageLabels: { ...informationLabels, yes: "Yes", no: "NO" }
  };
  const { FormLabels: formLabels, PageLabels: pageLabels } = labels;
  const locale = "en-us";
  const allRules = {
    ...CreatorForm.rules(informationLabels),
    ...CreatorForm.communicationRules(communicationPreferencesLabels)
  };
  const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
  const { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url } = allRules;
  const rules: Rules = { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url };
  const interestedCreator = {
    nucleusId: 1234567,
    defaultGamerTag: "RiffleShooter",
    originEmail: "<EMAIL>",
    dateOfBirth: dateOfBirthAfter18years,
    contentUrls: [{ url: "", followers: "" }],
    country: {
      value: "CA",
      label: "Canada",
      name: "Canada"
    }
  };
  const countries = [aCountry(), aCountry(), interestedCreator.country];
  const languages = [aLanguage({ value: "en", label: "English" })];
  const locales = [
    {
      value: "en_US",
      label: "English",
      id: "a0dK000000dacrfIAA"
    },
    {
      value: "es_ES",
      label: "Español",
      id: "a0dK000000dzdPtIAI"
    },
    {
      value: "ja_JP",
      label: "日本語",
      id: "a0dK000000dade5IAA"
    }
  ];
  const metadataService = {
    getCountriesMatching: jest.fn().mockResolvedValue(countries),
    getLanguages: jest.fn().mockResolvedValue(languages),
    getLocales: jest.fn().mockResolvedValue(locales)
  } as unknown as MetadataService;
  const submittedContentService = { validateContent: jest.fn() } as unknown as SubmittedContentService;
  const interestedCreatorsService = { saveApplication: jest.fn() } as unknown as InterestedCreatorsService;
  const connectedAccountsService = { getConnectedAccounts: jest.fn() } as unknown as ConnectedAccountsService;
  const configuration = { metadataClient: {}, applicationsClient: {} } as Configuration;
  const interestedCreatorsInformationPageProps: InterestedCreatorsInformationPageProps = {
    formLabels,
    pageLabels,
    handleCancelRegistration: jest.fn(),
    stableDispatch: jest.fn(),
    setShowConfirmation: jest.fn(),
    locale,
    rules,
    layout: informationLabels.layout,
    interestedCreator,
    router: useRouter(),
    analytics: {} as unknown as BrowserAnalytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    state: {},
    errorHandling: jest.fn(),
    configuration: configuration,
    redirectedToNextStepUrl: "/interested-creators/creator-types",
    onClose: jest.fn(),
    showConfirmation: true,
    showAddConfirmation: false,
    setShowAddConfirmation: jest.fn(),
    connectAccountLabels: ConnectAccountLabels,
    accountToRemove: "",
    setAccountToRemove: jest.fn(),
    showRemoveAccountModal: false,
    setShowRemoveAccountModal: jest.fn(),
    pages: [],
    connectAccounts: [],
    errorToast: jest.fn(),
    warning: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (MetadataService as jest.Mock).mockReturnValue(metadataService);
    (SubmittedContentService as jest.Mock).mockReturnValue(submittedContentService);
    (InterestedCreatorsService as jest.Mock).mockReturnValue(interestedCreatorsService);
    (ConnectedAccountsService as jest.Mock).mockReturnValue(connectedAccountsService);
  });

  it("shows placeholders when no creator information has been entered", async () => {
    (connectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      { type: "YOUTUBE", isExpired: false, username: "Hariraj" }
    ]);

    (useRouter as jest.Mock).mockImplementation(() => ({ locale }));
    render(<InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} />);

    expect(screen.getByPlaceholderText(formLabels.firstName)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(formLabels.lastName)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(formLabels.preferredEmail)).toBeInTheDocument();
    expect(screen.getByPlaceholderText(formLabels.dateOfBirth)).toBeInTheDocument();
    expect(screen.getByText(formLabels.country)).toBeInTheDocument();
    expect(screen.getByLabelText(formLabels.language)).toBeInTheDocument();
  });

  it("calls its submit handler and redirects to the next page", async () => {
    const newInterestedCreator = {
      ...interestedCreator,
      firstName: Random.firstName(),
      lastName: Random.lastName(),
      country: { name: "Canada", label: "Canada", value: "CA" },
      contentUrls: [{ url: "https://www.google.com", followers: "" }],
      contentLanguages: [
        {
          value: "en",
          label: "English",
          id: "a0LK0000008epjzMAA"
        }
      ]
    };
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const scanResult = {
      url: newInterestedCreator.contentUrls[0].url,
      isSecure: true
    };
    const urlScanResult = { results: [scanResult] };
    (connectedAccountsService.getConnectedAccounts as jest.Mock).mockResolvedValue([
      {
        name: Random.firstName(),
        disconnected: false,
        username: Random.string(),
        id: Random.uuid(),
        type: "FACEBOOK",
        uri: Random.url(),
        thumbnail: Random.imageUrl(),
        isExpired: true,
        accountId: Random.uuid()
      }
    ]);
    (interestedCreatorsService.saveApplication as jest.Mock).mockResolvedValue(() => Promise.resolve());
    (submittedContentService.validateContent as jest.Mock).mockResolvedValue(urlScanResult);

    renderPage(
      <InterestedCreatorsInformationPage
        {...{
          ...interestedCreatorsInformationPageProps,
          interestedCreator: newInterestedCreator,
          analytics
        }}
      />
    );
    const nextButton = screen.getByRole("button", { name: formLabels.next });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
      expect(interestedCreatorsInformationPageProps.router.push).toHaveBeenCalledWith(
        "/interested-creators/creator-types"
      );
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledTimes(1);
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledWith({
        ...newInterestedCreator,
        country: { ...interestedCreator.country },
        preferredEmail: "<EMAIL>",
        preferredLanguage: {}
      });
    });
  });

  it("is accessible", async () => {
    const { container } = render(<InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} />);

    const results = await act(() => axe(container));

    expect(results).toHaveNoViolations();
  });

  it("shows 'Language for Communication' default value based on locale", async () => {
    render(<InterestedCreatorsInformationPage {...interestedCreatorsInformationPageProps} />);

    expect(await screen.findByText(formLabels.languageTitle)).toBeInTheDocument();
    expect(await screen.findByText(/English$/i)).toBeInTheDocument();
  });

  it("shows 'Language for Communication' value based on creator information", async () => {
    const customInterestedCreator = {
      ...interestedCreator,
      preferredLanguage: { code: "ja_JP", name: "日本語" }
    };

    render(
      <InterestedCreatorsInformationPage
        {...interestedCreatorsInformationPageProps}
        interestedCreator={customInterestedCreator}
      />
    );

    expect(await screen.findByText(formLabels.languageTitle)).toBeInTheDocument();
    expect(await screen.findByText(/日本語/i)).toBeInTheDocument();
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' flag enabled", () => {
    it("pre-populates country and content urls", async () => {
      const interestedCreatorInformation = {
        nucleusId: 1234567,
        defaultGamerTag: "RiffleShooter",
        originEmail: "<EMAIL>",
        dateOfBirth: dateOfBirthAfter18years,
        countryCode: "CA",
        contentAccounts: [
          {
            url: "https://qa-creatornetwork.ea.com/",
            followers: ""
          },
          {
            url: "https://dev-creatornetwork.ea.com/",
            followers: ""
          }
        ]
      };
      const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;

      render(
        <InterestedCreatorsInformationPage
          {...{
            ...interestedCreatorsInformationPageProps,
            interestedCreator: interestedCreatorInformation,
            analytics
          }}
          INTERESTED_CREATOR_REAPPLY_PERIOD
        />
      );

      waitFor(async () => {
        expect(await screen.findByText("Canada")).toBeInTheDocument();
        expect(
          await screen.findByDisplayValue(interestedCreatorInformation.contentAccounts[0].url)
        ).toBeInTheDocument();
        expect(
          await screen.findByDisplayValue(interestedCreatorInformation.contentAccounts[1].url)
        ).toBeInTheDocument();
      });
    });
  });
});
