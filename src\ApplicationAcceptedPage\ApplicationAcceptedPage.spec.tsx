import React from "react";
import { BrowserAnalytics } from "../utils/types";
import { act, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import ApplicationAcceptedPage, { ApplicationAcceptedPageProps } from "./ApplicationAcceptedPage";
import { applicationAcceptedLabels } from "../../test/translations/ApplicationAccepted";
import Random from "../../test/factories/Random";

describe("ApplicationAcceptedPage", () => {
  const locale = "en-us";
  const analytics = { checkedApplicationStatus: jest.fn() } as unknown as BrowserAnalytics;
  const applicationAcceptedPageProps: ApplicationAcceptedPageProps = {
    labels: applicationAcceptedLabels,
    locale,
    analytics,
    applicationAcceptedThumbnail: Random.imageUrl(),
    onButtonClick: jest.fn()
  };
  beforeEach(() => jest.clearAllMocks());

  it("shows interested creator application accepted labels", async () => {
    const { title, descriptionPara1, descriptionPara2, completeYourProfile } = applicationAcceptedLabels;

    render(<ApplicationAcceptedPage {...applicationAcceptedPageProps} />);

    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(descriptionPara1)).toBeInTheDocument();
    expect(screen.getByText(descriptionPara2)).toBeInTheDocument();
    expect(screen.getByText(completeYourProfile)).toBeInTheDocument();
  });

  it("calls analytics when the locale changes", async () => {
    render(<ApplicationAcceptedPage {...applicationAcceptedPageProps} />);

    await waitFor(() => {
      expect(analytics.checkedApplicationStatus).toHaveBeenCalledTimes(1);
      expect(analytics.checkedApplicationStatus).toHaveBeenCalledWith({ locale, status: "Accepted" });
    });
  });

  it("navigates to the correct path when the Complete profile button is clicked", async () => {
    render(<ApplicationAcceptedPage {...applicationAcceptedPageProps} />);

    await userEvent.click(screen.getByRole("button", { name: applicationAcceptedLabels.completeYourProfile }));

    expect(applicationAcceptedPageProps.onButtonClick).toHaveBeenCalledTimes(1);
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<ApplicationAcceptedPage {...applicationAcceptedPageProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
