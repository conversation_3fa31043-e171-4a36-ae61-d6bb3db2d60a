import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import NoAccountPage from "./NoAccountPage";

const meta: Meta<typeof NoAccountPage> = {
  title: "Component Library/No Account Component",
  component: NoAccountPage,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof NoAccountPage>;

export const NoAccountComponent: Story = {
  args: {
    email: "<EMAIL>",
    state: {
      isLoading: false
    },
    labels: {
      close: "Close",
      title: "Sorry!",
      creatorNetwork: "Creator Network",
      requestToJoin: "Request to Jo<PERSON>",
      subTitlePart1: "You are currently logged in as",
      subTitlePart2: "It appears you are not a member of the EA Creator Program.",
      descriptionPara1:
        "If you’re interested in joining, you can submit a request to join and one of our EA Support a Creator team members will review your information.",
      noAccountThumbnailAltText: "No Account Thumbnail"
    },
    startApplication: () => {},
    noAccountThumbnail: "./img/Players-comp.png"
  }
};
