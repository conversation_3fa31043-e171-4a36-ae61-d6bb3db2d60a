import React, { <PERSON>, memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { NextRouter } from "next/router";
import { Control, Controller, FieldValues, useFormContext } from "react-hook-form";
import classNames from "classnames";
import { Button } from "@eait-playerexp-cn/core-ui-kit";
import { BrowserAnalytics, Dispatch, ErrorHandling, useAsync } from "../../utils";
import { FranchiseItem } from "../InterestedCreatorsFranchisesYouPlayPage/InterestedCreatorsFranchisesYouPlayPage";
import CheckboxCards from "../../cards/CheckboxCards/CheckboxCards";
import { ConfigurationType, FranchisesYouPlayFallbackImages, FranchisesYouPlayFormLabels } from "../FranchisesYouPlay";
import Search from "../../Search/Search";
import Footer from "../../Footer/Footer";
import { Required } from "../../utils/CreatorForm";
import InterestedCreatorsService, { InterestedCreator } from "../../Browser/InterestedCreatorsService";
import Form from "../../utils/Form";

const PAGE_SIZE = 8;

type FranchisesOptions = {
  value: string;
  label: string;
  image: string;
  checked: boolean;
};

type SecondaryFranchiseInputsProps = {
  name: string;
  items: FranchisesOptions[];
  values: FranchiseItem[];
  control: Control;
  disabled: boolean;
  secondaryFranchisefallbackImage: string;
  basePath?: string;
};

const SecondaryFranchiseInputs: FC<SecondaryFranchiseInputsProps> = ({
  name,
  items = [],
  values = [],
  control,
  disabled,
  secondaryFranchisefallbackImage,
  basePath
}) => {
  return (
    <>
      {disabled ? (
        <img
          alt="Franchise image"
          className={"secondary-franchise-image-unselected"}
          src={secondaryFranchisefallbackImage}
        />
      ) : (
        !!items.length && (
          <Controller
            control={control}
            name={name}
            defaultValue={values}
            render={({ field }) => (
              <CheckboxCards
                errorMessage={""}
                readOnly={false}
                selectAlternateItem={false}
                {...field}
                items={items}
                disabled={disabled}
                basePath={basePath}
              />
            )}
          />
        )
      )}
    </>
  );
};

type InterestedCreatorsFranchisesYouPlayInputsProps = {
  franchises: FranchiseItem[];
  interestedCreator: InterestedCreator;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  rules: FranchisesFormRules;
  setPrimaryFranchise: (a: FranchiseItem) => void;
  primaryFranchise: FranchiseItem;
  onClose: MouseEventHandler<HTMLButtonElement>;
  isPending: boolean;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  franchisesYouPlayFallbackImages: FranchisesYouPlayFallbackImages;
  basePath?: string;
};

const InterestedCreatorFranchisesYouPlayInputs = memo(function InterestedCreatorFranchisesYouPlayInputs({
  franchises,
  interestedCreator,
  franchisesYouPlayFormLabels,
  rules,
  setPrimaryFranchise,
  primaryFranchise,
  onClose,
  isPending,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  franchisesYouPlayFallbackImages,
  basePath
}: InterestedCreatorsFranchisesYouPlayInputsProps) {
  const [showLoadMore, setShowLoadMore] = useState<boolean>(false);
  const [isDisabledSubmit, setDisableSubmit] = useState<boolean>(true);
  const [franchiseOptions, setFranchiseOptions] = useState([]);
  const [selectedFranchise, setSelectedFranchise] = useState<FranchiseItem | null>(null);
  const methods = useFormContext();
  const { control } = methods;

  const initialFranchise = useMemo(() => {
    if (interestedCreator.preferredFranchises) {
      const primaryFranchise = interestedCreator.preferredFranchises.find(
        (preferredFranchise) => preferredFranchise.type === "PRIMARY"
      );
      const selectedFranchise = franchises.find((franchise) => franchise.value === primaryFranchise?.id);
      if (INTERESTED_CREATOR_REAPPLY_PERIOD && selectedFranchise) {
        setSelectedFranchise(selectedFranchise);
      }
      return selectedFranchise;
    }
  }, [interestedCreator, franchises]);

  useEffect(() => {
    if (INTERESTED_CREATOR_REAPPLY_PERIOD && selectedFranchise) {
      updatePrimaryCard(selectedFranchise);
    }
  }, [selectedFranchise]);

  const secondaryFranchises: Array<{
    value: string;
    label: string;
    image: string;
  }> = useMemo(() => {
    if (interestedCreator.preferredFranchises) {
      const preferredSecondaryFranchise: Array<{
        value: string;
        label: string;
        image: string;
      }> = [];
      interestedCreator.preferredFranchises.map((secondaryFranchise) => {
        if (secondaryFranchise.type === "SECONDARY") {
          franchiseOptions.find((franchise: { value: string; label: string; image: string }) => {
            if (franchise?.value === secondaryFranchise.id) {
              preferredSecondaryFranchise.push({
                value: franchise.value,
                label: franchise.label,
                image: franchise.image
              });
            }
          });
        }
      });
      return preferredSecondaryFranchise;
    }
    return [];
  }, [interestedCreator, franchiseOptions]);

  const secondaryProps = {
    name: "secondaryFranchise",
    basePath,
    control: control,
    values: secondaryFranchises,
    disabled: initialFranchise === undefined && primaryFranchise.value === "" && primaryFranchise.label === "",
    items: franchiseOptions.map((item: { value: string; label: string; image: string }) => {
      return {
        ...item,
        image: `${basePath}${item.image}`,
        checked: secondaryFranchises?.filter((franchiseItem) => franchiseItem.value === item.value).length > 0
      };
    }),
    secondaryFranchisefallbackImage: franchisesYouPlayFallbackImages.secondaryFranchisefallbackImage
  };

  const addFranchiseOptions = () => {
    if (franchises.length > franchiseOptions.length) {
      if (franchises.length - franchiseOptions.length > PAGE_SIZE) {
        setFranchiseOptions(
          franchiseOptions.concat(franchises.slice(franchiseOptions.length, franchiseOptions.length + PAGE_SIZE) as [])
        );
      } else {
        setFranchiseOptions(
          franchiseOptions.concat(franchises.slice(franchiseOptions.length, franchises.length) as [])
        );
      }
    }
  };

  useEffect(() => {
    if (franchises.length > PAGE_SIZE) {
      setFranchiseOptions(franchises.slice(0, PAGE_SIZE) as []);
    } else {
      setFranchiseOptions(franchises as []);
    }
  }, [franchises]);

  useEffect(() => {
    if (franchises.length > franchiseOptions.length) {
      setShowLoadMore(true);
    } else {
      setShowLoadMore(false);
    }
  }, [franchiseOptions, franchises]);

  const updatePrimaryCard = (selectedItem: { value: string; label: string; image: string }) => {
    setPrimaryFranchise(selectedItem);
    setDisableSubmit(false);
  };

  const buttons = useMemo(
    () => ({
      cancel: franchisesYouPlayFormLabels?.buttons?.cancel ?? "",
      next: franchisesYouPlayFormLabels?.buttons?.submit ?? ""
    }),
    [franchisesYouPlayFormLabels]
  );

  return (
    <>
      <div className="franchises-you-play-form">
        <div className="franchises-you-play">
          <div className="mg-ic-ui-primary-franchise-container">
            <div className="mg-ic-ui-primary-franchise">
              <h4 className="mg-ic-ui-primary-franchise-title">{franchisesYouPlayFormLabels.primaryFranchiseTitle}</h4>
              <div className="mg-ic-ui-primary-franchise-subtitle">
                {franchisesYouPlayFormLabels.primaryFranchiseSubTitle}
              </div>
            </div>
            {franchises && !!franchises.length && (
              <Controller
                control={control}
                name="primaryFranchise"
                rules={rules.primaryFranchise}
                defaultValue={initialFranchise}
                render={({ field, fieldState: { error } }) => (
                  <Search
                    errorMessage={(error && error.message) || ""}
                    disabled={false}
                    label={INTERESTED_CREATOR_REAPPLY_PERIOD && initialFranchise ? initialFranchise.label : ""}
                    {...field}
                    onChange={(item) => {
                      updatePrimaryCard(item);
                      field.onChange(item);
                    }}
                    options={franchises}
                    placeholder={franchisesYouPlayFormLabels.labels.primaryFranchise}
                  />
                )}
              />
            )}

            <div className="ic-ui-primary-franchise-option">
              <img
                alt="Selected Franchise image"
                className={classNames(
                  {
                    "ic-ui-primary-franchise-selected": primaryFranchise.value
                  },
                  "ic-ui-primary-franchise-option-image"
                )}
                src={
                  (primaryFranchise && primaryFranchise.image && `${basePath}${primaryFranchise.image}`) ||
                  (initialFranchise && initialFranchise.image && `${basePath}${initialFranchise.image}`) ||
                  franchisesYouPlayFallbackImages.primaryFranchisefallbackImage
                }
              />
            </div>
          </div>
          <div className="mg-ic-ui-sc-franchise-container">
            <div
              className={classNames({
                "mg-ic-ui-secondary-franchise-disabled": !primaryFranchise && !initialFranchise
              })}
            >
              <h4 className="mg-ic-ui-primary-franchise-title">
                {franchisesYouPlayFormLabels.secondaryFranchiseTitle}
              </h4>
              <div className="mg-ic-ui-primary-franchise-subtitle">
                {franchisesYouPlayFormLabels.secondaryFranchiseSubTitle}
              </div>
            </div>
            {secondaryProps && <SecondaryFranchiseInputs {...secondaryProps} />}
            {primaryFranchise.value && primaryFranchise.label && showLoadMore ? (
              <div className="ic-ui-secondary-franchise-load-more">
                <Button variant="secondary" size="sm" onClick={addFranchiseOptions}>
                  {franchisesYouPlayFormLabels.labels.loadMore}
                </Button>
              </div>
            ) : (
              <div className="pb-meas16"></div>
            )}
          </div>
        </div>
      </div>
      <Footer {...{ buttons, onCancel: onClose, disableSubmit: isDisabledSubmit || isPending, isPending }} />
    </>
  );
});

export type FranchisesFormRules = {
  primaryFranchise: Required;
};

const franchisesFormRules = (franchisesYouPlayFormLabels:FranchisesYouPlayFormLabels): FranchisesFormRules => {
  return {
    primaryFranchise: {
      required: franchisesYouPlayFormLabels.messages.primaryFranchise
    }
  };
};

type InterestedCreatorsFranchisesYouPlayFormProps = {
  franchises: FranchiseItem[];
  interestedCreator: InterestedCreator;
  franchisesYouPlayFormLabels: FranchisesYouPlayFormLabels;
  stableDispatch: Dispatch;
  onClose: () => void;
  router: NextRouter;
  analytics: BrowserAnalytics;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  errorHandling: ErrorHandling;
  redirectedToNextStepUrl: string;
  basePath?: string;
  configuration:ConfigurationType;
  franchisesYouPlayFallbackImages:FranchisesYouPlayFallbackImages;
};

const InterestedCreatorsFranchisesYouPlayForm: FC<InterestedCreatorsFranchisesYouPlayFormProps> = ({
  franchises,
  interestedCreator,
  franchisesYouPlayFormLabels,
  stableDispatch,
  onClose,
  router,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  errorHandling,
  redirectedToNextStepUrl,
  configuration,
  franchisesYouPlayFallbackImages,
  basePath,
}) => {
  const [primaryFranchise, setPrimaryFranchise] = useState<FranchiseItem>({ value: "", label: "", image: "" });
  const rules: FranchisesFormRules = franchisesFormRules(franchisesYouPlayFormLabels);
 const interestedCreatorsService = new InterestedCreatorsService(configuration.applicationsClient)
  const defaultValues = useMemo(() => {
    const { preferredFranchises = undefined } = interestedCreator;
    return { preferredFranchises };
  }, [interestedCreator]);

  const updateInterestedCreator = useCallback(
    async (data:FieldValues) => {
      const interestedCreatorPayload = {
        ...interestedCreator,
        preferredFranchises: Array<{ id: string; type: string }>(),
        creatorProgram: {
          code: configuration.programCode
        }
      };

     if(data.primaryFranchise.value) {interestedCreatorPayload.preferredFranchises.push({ id: data.primaryFranchise.value, type: "PRIMARY" })}
     if(data.secondaryFranchise)
        { data.secondaryFranchise.map((secondaryFranchise:{value:string}) => {
          const franchise = { id: secondaryFranchise.value, type: "SECONDARY" }
          interestedCreatorPayload.preferredFranchises.push(franchise);
        });}
      try {
        await interestedCreatorsService.saveInterestedCreatorInformation(interestedCreatorPayload as InterestedCreator);
        if(analytics.continuedCreatorApplication) {analytics.continuedCreatorApplication({ locale: router.locale ?? '', page: location.pathname, finalStep: true })}
        router.push(redirectedToNextStepUrl);
      }
      catch (error) {
        errorHandling(stableDispatch, error as Error);
      }
    },
    [interestedCreator, stableDispatch, router, configuration.programCode]
  );

  const { pending: isPending, execute: submitHandler } = useAsync(updateInterestedCreator, false);

  return (
    <div className="interested-creators-ui-franchises-you-play-form">
      <Form mode="onChange" onSubmit={submitHandler} defaultValues={defaultValues}>
        <InterestedCreatorFranchisesYouPlayInputs
          {...{
            franchises,
            interestedCreator,
            franchisesYouPlayFormLabels,
            rules,
            setPrimaryFranchise,
            primaryFranchise,
            onClose,
            isPending,
            INTERESTED_CREATOR_REAPPLY_PERIOD,
            franchisesYouPlayFallbackImages,
            basePath
          }}
        />
      </Form>
    </div>
  );
};

export default InterestedCreatorsFranchisesYouPlayForm;
