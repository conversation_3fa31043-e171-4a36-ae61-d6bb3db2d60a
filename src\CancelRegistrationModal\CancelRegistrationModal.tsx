import React, { FC, memo, MutableRefObject, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dalBody,
  <PERSON>dal<PERSON>lose<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>le,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";
import Link from "next/link";

export type CancelRegistrationModalProps = {
  labels: {
    title: string;
    yes: string;
    no: string;
    close: string;
    confirmationDesc1: string;
    confirmationDesc2?: string;
  };
  handleCancelRegistration?: () => void;
  handleModalClose: () => void;
};

export type FooterButtonsProps = {
  yes: string;
  no: string;
  onNo: () => void;
  onYes?: () => void;
  cancelButtonRef: MutableRefObject<HTMLButtonElement | null>;
};

export const FooterButtons: FC<FooterButtonsProps> = memo(function FooterButtons({
  yes,
  no,
  onNo,
  onYes,
  cancelButtonRef
}) {
  return (
    <>
      <Button variant="tertiary" dark size="md" onClick={onNo} ref={cancelButtonRef}>
        {no}
      </Button>
      {onYes ? (
        <Button onClick={onYes}>{yes}</Button>
      ) : (
        <Link href="/api/logout" className="btn btn-primary btn-md">
          {yes}
        </Link>
      )}
    </>
  );
});

export const CancelRegistrationModal: FC<CancelRegistrationModalProps> = ({
  labels,
  handleCancelRegistration,
  handleModalClose
}) => {
  const cancelButtonRef = useRef<HTMLButtonElement | null>(null);
  return (
    <>
      <ModalV2 closeButtonRef={cancelButtonRef}>
        <ModalHeader>
          <ModalTitle>{labels.title}</ModalTitle>
          <ModalCloseButton ariaLabel={labels.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
        </ModalHeader>
        <ModalBody>
          <p>{labels.confirmationDesc1}</p>
          {labels.confirmationDesc2 && (
            <p className="cancel-registration-modal-confirmation-additional-desc">{labels.confirmationDesc2}</p>
          )}
        </ModalBody>
        <ModalFooter showDivider>
          <FooterButtons
            yes={labels.yes}
            no={labels.no}
            onNo={handleModalClose}
            onYes={handleCancelRegistration}
            cancelButtonRef={cancelButtonRef}
          />
        </ModalFooter>
      </ModalV2>
    </>
  );
};

export default CancelRegistrationModal;
