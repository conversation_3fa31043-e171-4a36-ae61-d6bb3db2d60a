.information.interested-creators-ui-information {
  @apply mb-meas16 md:self-center;
}

.interested-creators-ui-information-language {
  @apply border-t border-[rgba(255,255,255,0.33)];
}

.interested-creators-ui-information-container {
  @apply w-[292px] pb-meas15 md:w-[635px]
}

.interested-creators-ui .content-languages,
.interested-creators-ui .content-in-center {
  @apply flex w-[100%] flex-col md:w-[635px] md:items-center ;
}

.interested-creators-ui .content-in-center.language .select-box {
  @apply w-full !important;
}

.interested-creators-ui .content-in-center.social-media {
  @apply border-t-0 pt-meas0;
}

.interested-creators-ui .content-language.mg-ic-ui-communication-title {
  @apply font-display-bold;
}
.interested-creators-ui .mg-ic-ui-communication-description {
  @apply pt-meas13 md:pt-meas6;
}
.interested-creators-ui .content-language .select-label,
.interested-creators-ui .language .select-label {
  @apply self-start font-display-bold invisible;
}
.interested-creators-ui-information-content-media-container {
  @apply mt-meas32 flex w-full flex-col gap-meas8 border-t border-[rgba(255,255,255,0.33)] py-meas16;
}

.interested-creators-ui-information-content-media-container > .interested-creators-ui-information-content-media-title {
  @apply text-left font-display-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4 text-gray-10 md:text-center;
}

.interested-creators-ui-information-content-media-container > p {
  @apply my-meas8 font-display-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default text-gray-10 md:text-center;
}

.interested-creators-ui-information-content-media {
  @apply w-full;
}

.interested-creators-ui-information-content-media > li > label:first-child {
  @apply col-span-4 md:col-span-3;
}

.interested-creators-ui-information-content-media li > label:last-of-type {
  @apply col-span-2 w-[154px] md:col-span-1;
}

.interested-creators-ui-information-content-media > li {
  @apply mb-meas7 grid grid-cols-5 gap-x-meas20 gap-y-meas12;
  grid-template-rows: auto;
}

.interested-creators-ui-information-content-media > li > label:nth-child(2) {
  @apply whitespace-nowrap;
}

.interested-creators-ui-content-media-delete {
  @apply col-span-2 mt-[14px] place-self-center md:col-span-1 md:my-auto;
}

.interested-creators-ui .mg-ic-ui-header-logo a > span {
  @apply font-display-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}

.interested-creators-ui form .multi-select-button,
.interested-creators-ui form .input-text-field::placeholder {
  color: #a6a6a6;
}

.interested-creators-ui form .multi-select-button {
  @apply justify-center;
}

.interested-creators-ui form .input-box-label {
  @apply font-display-bold;
}

.interested-creators-ui-content-url-add-more {
  @apply mt-meas13 mb-meas18 flex w-fit cursor-pointer md:mb-[74px] xl:mt-meas16 xl:mb-[60px];
}
.interested-creators-ui-content-url-add-more-icon {
  @apply h-meas10 w-meas10 text-gray-10;
}
.interested-creators-ui-content-url-add-more-text {
  @apply ml-[6px] self-center font-display-bold xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large text-gray-10 leading-4 tracking-[1px];
}
.interested-creators-ui-content-url-icon {
  @apply h-[18px] text-gray-10;
}

.interested-creators-ui .interested-creators-ui-information-description {
  @apply flex justify-center text-center font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large mb-meas16;
}

.interested-creators-ui-information-sub-description {
  @apply w-[292px] md:w-[630px] xs:text-mobile-body-large md:text-mobile-body-large lg:text-mobile-body-large text-gray-10;
}

.interested-creators-ui-information-description{
 @apply text-center;
}

.interested-creators-ui-information-description .gamer-tag {
  @apply font-text-bold;
}

.ic-ui-add-account-container {
  @apply pb-meas16;
}

.add-account-description {
  @apply flex flex-row pb-meas0 font-text-regular xs:text-mobile-body-default md:text-tablet-body-default 
  lg:text-desktop-body-default pt-meas10 leading-6 font-normal text-center;
}

.interested-creators-ui .ic-ui-connect-accounts {
  @apply xl:w-[inherit];
}

.interested-creators-ui .ic-ui-connect-accounts-form,
.interested-creators-ui .ic-ui-connect-accounts,
.interested-creators-ui .mg-ic-ui-connected-accounts-container {
  @apply xl:w-[inherit];
}

.interested-creators-ui .mg-ic-ui-connected-accounts-container {
  @apply mt-meas0 mb-meas0 border-0 pt-meas10;
}

.interested-creators-ui .mg-ic-ui-connected-accounts-title {
  @apply pb-meas10;
}

.interested-creators-ui .account-card-channel-name {
  @apply pb-meas0;
}
.account-card-channel-name{
  @apply text-mobile-body-default
}

/**This will be deleted once we integrate webite Or additional links section*/
.ic-ui-add-account-container ~ .social-media > .interested-creators-ui-information-content-media-container {
  @apply mt-meas0;
}

.add-account-social-media-header {
  @apply leading-7 xl:leading-9 text-[1.5rem] text-center;
}

.interested-creators-ui-information-additional-content-container {
  @apply flex w-full flex-col gap-meas10 pb-meas16 md:pb-meas38;
}

.interested-creators-ui-information-additional-content-title {
  @apply text-left font-display-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4 text-gray-10 md:text-center leading-7 text-[1.5rem] tracking-[1px];
}

.interested-creators-ui-information-additional-content-urls {
  @apply flex w-full flex-col self-start;
}

.interested-creators-ui-information-additional-content-url {
  @apply mb-meas0 flex w-full;
}

.interested-creators-ui-information-additional-content-url:nth-child(n + 2) {
  @apply mt-meas10;
}

/** Added this compound selector in order to override input component label */
.interested-creators-ui-information-additional-content-url > label:last-of-type {
  @apply col-span-2 w-full flex-[1] md:flex-[0.9];
}

.interested-creators-ui-information-additional-content-url-without-delete > label:last-of-type {
  @apply md:flex-1;
}

.interested-creators-ui-information-additional-content-add-more {
  @apply flex w-fit cursor-pointer self-start items-center;
}

.interested-creators-ui-information-additional-content-delete {
  @apply mt-meas10 flex h-meas20 flex-[0.2] items-center justify-center md:flex-[0.1];
}

.interested-creators-ui-information-additional-content-description {
  @apply font-normal text-[1rem] leading-6 md:text-center font-text-regular text-gray-10;
}

/** Added the below css for overriding the ConnectAccountsForm component used in My Profile(My accounts & Add accounts sections are there)*/
.ic-ui-add-account-container .mg-ic-ui-connected-accounts-title {
  @apply font-bold text-[1.5rem]
}

.ic-ui-add-account-container .content-in-center{
  @apply w-full;
}

.ic-ui-add-account-container .mg-ic-ui-connected-accounts-container {
  @apply w-auto;
}

.ic-ui-add-account-card .ic-ui-connected-acc-card-container {
  @apply self-center xl:self-start grid-cols-1 md:grid-cols-1 xl:grid-cols-3
}

.interested-creators-ui .multi-select-menu {
  @apply transition-all duration-300 ease-in-out
}

.interested-creators-ui .multi-select-menu:has(.multi-select-items) {
  @apply md:mb-meas10;
}

.ic-ui-myprofile-view div.ic-ui-connected-acc-card-container {
  @apply items-start md:items-center
  md:justify-center grid grid-cols-2 md:grid-cols-3 gap-meas10 md:gap-meas20;
}

.connect-account-insta-steps {
  @apply text-link;
}

.connect-account-insta-warning {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
