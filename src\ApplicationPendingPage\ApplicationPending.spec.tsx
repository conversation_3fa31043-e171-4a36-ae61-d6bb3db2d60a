import { axe } from "jest-axe";
import { act, render } from "@testing-library/react";
import ApplicationPendingPage, { ApplicationPendingPageProps } from "./ApplicationPendingPage";
import { applicationPendingLabels } from "../../test/translations/ApplicationPending";
import { BrowserAnalytics } from "../utils";
import React from "react";

jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));

describe("ApplicationPendingPage", () => {
  const applicationPendingProps: ApplicationPendingPageProps = {
    labels: applicationPendingLabels,
    locale: "en-us",
    analytics: { checkedApplicationStatus: jest.fn() } as BrowserAnalytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: true,
    emailId: "<EMAIL>",
    canApply: true,
    submittedDate: "2023-01-01",
    redirectedToInformationForm: "/",
    redirectedToMain: "/",
    applicationPendingThumbnail: "https://via.placeholder.com/150"
  };

  it("is accessible", async () => {
    const { container } = render(<ApplicationPendingPage {...applicationPendingProps} />);

    const results = await act(() => axe(container));

    expect(results).toHaveNoViolations();
  });
});
