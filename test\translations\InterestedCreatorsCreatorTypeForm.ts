export const interestedCreatorsCreatorTypeFormLabels = {
  cancel: "Cancel",
  next: "Next",
  yes: "Yes",
  no: "No",
  close: "Close",
  title: "Are you sure you want to Can<PERSON>?",
  description:
    "if you change your mind, you can always come back and accept the invite later cancel and join back when you like.",
  interestedCreatorTitle: "Interested Creator Title",
  interestedCreatorDescription: "Interested Creator Description",
  confirmationDesc1: "Confirmation Description 1",
  confirmationDesc2: "Confirmation Description 2",
  modalConfirmationTitle: "Modal Confirmation Title",
  unhandledError: "Unhandled Error",
  requiredMessage: "This field is required",
  creatorsTypeLabels: [
    {
      value: "YOUTUBER",
      label: "Youtuber"
    },
    {
      value: "LIFESTYLE",
      label: "Lifestyle"
    },
    {
      value: "PHOTOGRAPHER",
      label: "Photographer"
    },
    {
      value: "DESIGNER_ARTIST",
      label: "Designer/Artist"
    },
    {
      value: "BLOGGER",
      label: "Blogger"
    },
    {
      value: "LIVE_STREAMER",
      label: "Live Streamer"
    },
    {
      value: "PODCASTER",
      label: "Podcaster"
    },
    {
      value: "COSPLAYER",
      label: "Cosplayer"
    },
    {
      value: "ANIMATOR",
      label: "Animator"
    },
    {
      value: "SCREENSHOTER",
      label: "Screenshoter"
    },
    {
      value: "OTHER",
      label: "Other"
    }
  ]
};
