import { type TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import SubmittedContentService from "./SubmittedContentService";
import Random from "../../test/factories/Random";

jest.mock("@eait-playerexp-cn/http-client");

describe("SubmittedContentService", () => {
  it("shows a content url validate", async () => {
    const client = { post: jest.fn() } as unknown as TraceableHttpClient;
    const service = new SubmittedContentService(client);
    const content = [Random.url(), Random.url()];

    await service.validateContent(content, "INTERESTED_CREATORS");

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith(`/api/verify-content-url?type=INTERESTED_CREATORS`, { body: content });
  });
});
