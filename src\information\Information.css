.mg-ic-ui-page-interested-creator {
  @apply flex h-full min-h-screen flex-col items-center text-gray-10 w-[292px] md:w-[630px];
}
.link {
  @apply cursor-pointer;
}
.mg-ic-ui-header-container {
  @apply flex w-full flex-col pt-meas7 md:mb-meas24;
}
.mg-ic-ui-header {
  @apply flex w-full flex-row pb-meas20 text-gray-10;
}
.mg-ic-ui-header-back {
  @apply block flex-1 justify-start xl:hidden;
}
.mg-ic-ui-header-logo {
  @apply hidden justify-between font-display-regular xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5 font-bold md:block xl:flex-1 xl:justify-start;
}
.mg-ic-ui-header-logo > a > span {
  @apply ml-meas4;
}
.mg-ic-ui-header-close {
  @apply mr-meas7 flex flex-1 justify-end md:mr-[51px] md:mt-[19px] xl:mr-meas8 xl:mt-[17px];
}
.mg-ic-ui-header-logo .icon-block {
  @apply m-meas4 inline;
}
.mg-ic-ui-header-logo .ea-logo {
  @apply inline h-meas20 w-meas20 fill-gray-10;
}
.mg-ic-ui-intro {
  @apply mt-meas10 text-center;
}
.mg-ic-ui-intro-title {
  @apply pb-meas5 font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 text-gray-10;
}
.mg-ic-ui-intro-description {
  @apply font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.mg-ic-ui-intro-description[data-disabled="true"] {
  @apply opacity-50;
}
/* Form container*/
.mg-ic-ui-page-interested-creator form {
  @apply flex w-11/12 flex-col items-center justify-center md:w-[630px];
}
/* Migration footer */
.mg-ic-ui-footer-container {
  @apply mt-meas16 w-full border-t border-[rgba(255,255,255,0.33)] py-meas16 pl-meas8 text-right xl:w-[1060px];
}

.mg-ic-ui-footer-container svg.icon.icon-hide {
  @apply hidden;
}
.input-box-label {
  @apply font-text-regular;
}
.mg-ic-ui-header-close .icon-block {
  @apply cursor-pointer;
}
.mg-ic-ui-header-close .icon-block svg {
  @apply md:h-meas9 md:w-meas9;
}
.stepper-back {
  @apply hidden xl:block;
}
