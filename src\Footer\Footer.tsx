import React, { memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react";
import { Button, Icon, rightArrow } from "@eait-playerexp-cn/core-ui-kit";

export type ButtonsProps = {
  cancel: string;
  next: string;
};
export type FooterProps = {
  buttons: ButtonsProps;
  onCancel?: MouseEventHandler<HTMLButtonElement>;
  disableSubmit: boolean;
  isPending?: boolean;
};
export default memo(function Footer({ buttons, onCancel, disableSubmit, isPending }: FooterProps) {
  return (
    <div className="ic-ui-footer-container">
      <Button variant="tertiary" size="md" onClick={onCancel}>
        {buttons.cancel}
      </Button>
      <Button variant="primary" size="md" type="submit" spinner={isPending} disabled={disableSubmit}>
        {buttons.next} &nbsp;
        <Icon icon={rightArrow} className={isPending ? "icon icon-hide" : "icon"} />
      </Button>
    </div>
  );
});
