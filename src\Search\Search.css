.search-box button {
  @apply w-full overflow-visible text-left font-display-regular;
}

.search-box {
  @apply relative select-none xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}

.search-option-icon {
  @apply ml-meas4 mr-meas2 inline-block w-meas8;
}

.search-header {
  @apply relative flex h-meas18 cursor-pointer items-center justify-between rounded-sm border border-gray-70 bg-gray-10 p-meas4 leading-9 outline-none;
}
.disabled.search-header {
  @apply border-gray-30;
}
.error.search-header {
  @apply border-error-50;
}
.focused.search-header {
  @apply border-info-50 outline-none;
}
.search-header-title {
  @apply mr-meas4 flex w-meas38 items-center overflow-auto text-ellipsis whitespace-nowrap font-display-regular text-gray-50;
}

.disabled.search-header-title {
  @apply text-gray-30;
}
.selected.search-header-title {
  @apply text-gray-70;
}
.search-header-label {
  @apply w-meas38 overflow-auto text-ellipsis whitespace-nowrap;
}
.search-box .icon {
  @apply mx-meas0;
}
.search-selected {
  @apply text-gray-70;
}
.search-list {
  @apply absolute z-10 max-h-meas40 w-full rounded rounded-b-md rounded-t-none bg-gray-10 text-left shadow-gray-90-outline-4-8-apacity-0.15;
}
.form-error-message {
  @apply pt-meas2 font-text-regular xs:text-mobile-caption1 md:text-tablet-caption1 lg:w-[100px] lg:text-desktop-caption1 text-error-50;
}
.search-label {
  @apply pb-meas2 font-text-bold xs:text-mobile-caption1 md:text-tablet-caption1 lg:w-[100px] lg:text-desktop-caption1 text-gray-70;
}
.search-option-label {
  @apply overflow-auto text-ellipsis whitespace-nowrap;
}
.search-option-label-icon {
  @apply w-meas38;
}
.search-scroll-list {
  @apply max-h-meas40 overflow-y-scroll;
}

.search-list-item {
  @apply inline-flex w-full cursor-pointer items-center overflow-hidden text-ellipsis whitespace-nowrap p-meas4 text-gray-70 outline-none hover:bg-gray-10;
}
.selected.search-list-item {
  @apply bg-gray-10;
}

.search-list-item.no-result {
  @apply cursor-default;
}
.selected.list-item {
  @apply hover:bg-gray-10;
}
.search-header-icon {
  @apply mr-meas2 text-gray-50;
}
.search-text-field {
  @apply w-[292px] md:w-[262px] cursor-pointer rounded-sm p-meas4 font-text-regular xs:text-mobile-body-default 
  md:text-tablet-body-default lg:text-desktop-body-default text-gray-90 focus:border-gray-90 focus:outline-none
  bg-down-arrow-icon-background bg-no-repeat bg-[position:95%_center] bg-[length:30px];
}
