import React from "react";
import { render, screen } from "@testing-library/react";
import { NextRouter, useRouter } from "next/router";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { BrowserAnalytics } from "../utils";
import { interestedCreatorInformationFormLabels } from "../../test/translations/InterestedCreatorInformationForm";
import { informationLabels } from "../../test/translations/Information";
import { Configuration, InterestedCreatorInformationProps, Labels } from "./Information";
import { ConnectAccountLabels } from "../../test/translations/ConnectAccounts";
import InterestedCreatorInformation from "./Information";
import InterestedCreatorsInformationPage from "./InterestedCreatorsInformationPage/InterestedCreatorsInformationPage";

jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));
jest.mock("./InterestedCreatorsInformationPage/InterestedCreatorsInformationPage", () => {
  return jest.fn(() => <div data-testid="interested-creators-ui-information-page" />);
});
describe("Information", () => {
  const informationProp = { nucleusId: **********, originEmail: "", defaultGamerTag: "" };
  const analytics = {} as unknown as BrowserAnalytics;
  const labels: Labels = {
    formLabels: interestedCreatorInformationFormLabels.formLabels,
    infoLabels: informationLabels.infoLabels,
    translation: informationLabels.translation,
    pageLabels: { ...informationLabels, yes: "Yes", no: "NO" },
    layout: informationLabels.layout
  };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  const configuration = { metadataClient: {}, applicationsClient: {} } as Configuration;
  const router = { locale: "en-us", push: jest.fn() };
  const interestedCreatorsProps: InterestedCreatorInformationProps = {
    interestedCreator: { ...informationProp },
    handleCancelRegistration: jest.fn(),
    analytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    stableDispatch: jest.fn(),
    state: {
      onboardingSteps: steps
    },
    labels: labels,
    redirectedToNextStepUrl: "/interested-creators/creator-types",
    errorHandling: jest.fn(),
    onClose: jest.fn(),
    showConfirmation: true,
    setShowConfirmation: jest.fn(),
    configuration: configuration,
    router: router as unknown as NextRouter,
    locale: "en-us",
    showAddConfirmation: false,
    setShowAddConfirmation: jest.fn(),
    connectAccountLabels: ConnectAccountLabels,
    accountToRemove: "",
    setAccountToRemove: jest.fn(),
    showRemoveAccountModal: false,
    setShowRemoveAccountModal: jest.fn(),
    pages: [],
    connectAccounts: [],
    errorToast: jest.fn(),
    warning: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
  });

  it("renders the InterestedCreatorsInformationPage component", () => {
    render(<InterestedCreatorInformation {...interestedCreatorsProps} />);

    expect(screen.getByTestId("interested-creators-ui-information-page")).toBeInTheDocument();
  });

  it("passes correct props to InterestedCreatorsInformationPage", () => {
    render(<InterestedCreatorInformation {...interestedCreatorsProps} />);

    expect(InterestedCreatorsInformationPage).toHaveBeenCalledWith(
      expect.objectContaining({
        formLabels: interestedCreatorsProps.labels.formLabels,
        pageLabels: interestedCreatorsProps.labels.pageLabels,
        interestedCreator: interestedCreatorsProps.interestedCreator,
        rules: expect.any(Object),
        stableDispatch: interestedCreatorsProps.stableDispatch,
        router: interestedCreatorsProps.router,
        locale: interestedCreatorsProps.locale,
        analytics: interestedCreatorsProps.analytics,
        INTERESTED_CREATOR_REAPPLY_PERIOD: interestedCreatorsProps.INTERESTED_CREATOR_REAPPLY_PERIOD,
        state: interestedCreatorsProps.state,
        errorHandling: interestedCreatorsProps.errorHandling,
        layout: interestedCreatorsProps.labels.layout,
        redirectedToNextStepUrl: interestedCreatorsProps.redirectedToNextStepUrl,
        errorToast: interestedCreatorsProps.errorToast,
        configuration: interestedCreatorsProps.configuration
      }),
      {}
    );
  });
});
