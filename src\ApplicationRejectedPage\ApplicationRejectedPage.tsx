import React, { FC, useEffect, useMemo } from "react";
import classNames from "classnames";
import Rejected from "../styles/img/icons/Rejected";
import { BrowserAnalytics } from "../utils/types";
import RequestsToJoinStatusTable from "../RequestsToJoinStatusTable/RequestsToJoinStatusTable";
import Link from "next/link";

export type Labels = {
  title: string;
  descriptionPara1: string;
  descriptionPara2: string;
  descriptionPara3: string;
  descriptionPara4: string;
  backHome: string;
  email: string;
  programName: string;
  programLabel: string;
  status: string;
  submissionDate: string;
  closed: string;
  submissionReviewed: string;
  submissionReviewedDescription: string;
  reApplyTitle: string;
  reApplyDescription: string;
  reviewAndResubmit: string;
  close: string;
  creatorNetwork: string;
  returnToHomePage: string;
};
export type ApplicationRejectedPageProps = {
  labels: Labels;
  locale: string;
  analytics: BrowserAnalytics;
  emailId?: string;
  INTERESTED_CREATOR_REAPPLY_PERIOD?: boolean;
  canApply?: boolean;
  submittedDate?: string;
  reSubmitRequestDate?: string;
  logoutPath: string;
  redirectedToMain: string;
  redirectedToInformatioForm: string;
  applicationRejectedThumbnail: string;
};

const ApplicationRejectedPage: FC<ApplicationRejectedPageProps> = ({
  labels,
  locale,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  emailId,
  submittedDate,
  canApply,
  reSubmitRequestDate,
  redirectedToMain,
  logoutPath,
  redirectedToInformatioForm,
  applicationRejectedThumbnail
}) => {
  const {
    title,
    descriptionPara1,
    descriptionPara2,
    descriptionPara3,
    descriptionPara4,
    returnToHomePage,
    submissionReviewed,
    submissionReviewedDescription,
    closed,
    reApplyTitle,
    reApplyDescription,
    reviewAndResubmit
  } = labels;

  useEffect(() => {
    if (analytics.checkedApplicationStatus) {
      analytics.checkedApplicationStatus({ locale, status: "Rejected" });
    }
  }, [locale]);

  const rejectionTitle = useMemo(() => {
    if (!INTERESTED_CREATOR_REAPPLY_PERIOD) return title;

    if (canApply) return reApplyTitle;

    if (reSubmitRequestDate) return submissionReviewed;

    return title;
  }, [INTERESTED_CREATOR_REAPPLY_PERIOD, canApply, reApplyTitle, reSubmitRequestDate, submissionReviewed, title]);

  const rejectionDescription = useMemo(() => {
    if (canApply) return reApplyDescription;

    if (reSubmitRequestDate) return `${submissionReviewedDescription}`;

    return descriptionPara1;
  }, [canApply, descriptionPara1, reApplyDescription, reSubmitRequestDate, submissionReviewedDescription]);

  return (
    <div className="mg-ic-ui-page-interested-creator">
      <div>
        <div className="application-rejected-wrapper">
          {!INTERESTED_CREATOR_REAPPLY_PERIOD && (
            <img className="application-rejected-thumbnail" src={applicationRejectedThumbnail} alt="" />
          )}
          <div
            className={classNames("application-rejected-content-container", {
              "application-rejected-content-container-re-apply": INTERESTED_CREATOR_REAPPLY_PERIOD
            })}
          >
            <h3 className="application-rejected-title">{rejectionTitle}</h3>
            {INTERESTED_CREATOR_REAPPLY_PERIOD ? (
              <div className="application-rejected-description">{rejectionDescription}</div>
            ) : (
              <>
                <div className="application-rejected-descriptionPara1">{descriptionPara1}</div>
                <div className="application-rejected-descriptionPara2">{descriptionPara2}</div>
                <div className="application-rejected-descriptionPara3">{descriptionPara3}</div>
                <div className="application-rejected-descriptionPara4">{descriptionPara4}</div>
              </>
            )}
          </div>
          {INTERESTED_CREATOR_REAPPLY_PERIOD && (canApply || reSubmitRequestDate) && (
            <RequestsToJoinStatusTable
              requestsToJoinStatusTableLabels={labels}
              emailId={emailId ?? ""}
              INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
              submittedDate={submittedDate}
              statusIcon={Rejected}
              applicationStatus={closed}
            />
          )}

          {(!INTERESTED_CREATOR_REAPPLY_PERIOD || (INTERESTED_CREATOR_REAPPLY_PERIOD && !canApply)) && (
            <div className="application-rejected-back-home">
              <Link href={logoutPath} className="btn btn-primary btn-md">
                {returnToHomePage}
              </Link>
            </div>
          )}

          {INTERESTED_CREATOR_REAPPLY_PERIOD && canApply && (
            <div className="application-rejected-footer">
              <Link href={redirectedToMain} className="btn btn-md btn-secondary">
                {returnToHomePage}
              </Link>

              <Link href={redirectedToInformatioForm} className="btn btn-primary btn-md">
                {reviewAndResubmit}
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApplicationRejectedPage;
