import React, { useEffect } from "react";
import { memo, ReactElement } from "react";
import Link from "next/link";
import {
  CloseHandler,
  ERROR,
  onToastClose,
  State,
  toastContent,
  VALIDATION_ERROR,
  ValidationError
} from "../utils/index";
import { Button, Toast } from "@eait-playerexp-cn/core-ui-kit";

export type Dispatch = {
  type: string;
  data: boolean;
};

export type pageLabels = {
  title: string;
  subTitle: string;
  description: string;
  descriptionSuffix: string;
  button: string;
  alreadyApplied: string;
  alreadyAppliedSuffix: string;
  close: string;
};

type Labels = pageLabels & {
  creatorNetwork: string;
  interestedCreatorTitle: string;
  unhandledError: string;
};

export type InterestedCreatorsStartPageProps = {
  labels: Labels;
  stableDispatch: (param: Dispatch) => void;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  state: State;
  unhandledError: string;
  startThumbnail: string;
  startApplication: () => void;
};

export default memo(function InterestedCreatorsStartPage({
  labels,
  state,
  errorToast,
  stableDispatch,
  unhandledError,
  startThumbnail,
  startApplication
}: InterestedCreatorsStartPageProps) {
  const { title, subTitle, description, descriptionSuffix, button, alreadyApplied, alreadyAppliedSuffix } = labels;
  const { isError = false, isValidationError } = state as { isError: boolean; isValidationError: ValidationError[] };

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(<Toast header={unhandledError} content={isError ? isError : toastContent(isValidationError)} />, {
        onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
      });
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);

  return (
    <div className="mg-ic-ui-page-interested-creator">
      <div className="start-wrapper">
        <div className="start-thumbnail-container">
          <img className="start-thumbnail" src={startThumbnail} alt="" />
        </div>
        <div className="start-content-container">
          <h3 className="start-title">{title}</h3>
          <h4 className="start-sub-title">{subTitle}</h4>
          <div className="start-body">
            {description}{" "}
            <Link href="/api/accounts" className="start-ea-account">
              {descriptionSuffix}
            </Link>
          </div>
          <div className="start-apply-button">
            <Button variant="primary" size="md" onClick={startApplication}>
              {button}
            </Button>
          </div>
          <div className="start-already-applied-content-container">
            {alreadyApplied}{" "}
            <span onClick={startApplication} className="interested-creators-ui-already-applied">
              {alreadyAppliedSuffix}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
});
