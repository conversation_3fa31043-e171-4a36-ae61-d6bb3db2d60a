export const creatorTypeLabel = {
  FormLabels: {
    cancel: "Cancel",
    next: "Next",
    yes: "Yes",
    no: "No",
    close: "Close"
  },
  PageLabels: {
    unhandledError: "An error occurred. Please try again.",
    interestedCreatorTitle: "Choose your Creator Types",
    interestedCreatorDescription: "Select the types of creator that fits the content you create.",
    confirmationDesc1: "Pressing Yes will quit the registration process.",
    confirmationDesc2: "You can begin registration again at any point by visiting the Creator Network Website.",
    modalConfirmationTitle: "Are you sure you want to cancel registration?",
    title: "Are you sure you want to Cancel?",
    description:
      "if you change your mind, you can always come back and accept the invite later cancel and join back when you like.",
    close: "Close",
    cancel: "Cancel"
  }
};
