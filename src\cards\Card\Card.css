.ic-ui-card-container {
  @apply grid grid-cols-2 items-center justify-center gap-meas10 md:gap-meas16 mb-meas16 text-gray-10 md:grid-cols-3 xl:gap-x-[33px];
}
.ic-ui-card-col {
  @apply flex h-[170px] w-[130px] md:w-[183px] md:h-[220px] flex-col items-center justify-start;
}
.ic-ui-card-col[data-disabled="true"] {
  @apply opacity-50;
}
.ic-ui-check-container {
  @apply relative block cursor-pointer select-none;
}
.ic-ui-check-container input {
  @apply absolute h-meas0 w-meas0 cursor-pointer opacity-0;
}
.ic-ui-checkmark {
  @apply left-meas0 top-meas0 flex h-[130px] w-[130px] md:w-[183px] md:h-[183px] items-center justify-center rounded-[3px] border-[3px] border-navy-60 bg-navy-80 bg-contain bg-center bg-no-repeat duration-300 ease-in-out;
}
.ic-ui-checkmark .ic-ui-checkmark-box {
  @apply absolute right-meas0 top-meas0 fill-navy-60 text-gray-10;
}

.ic-ui-checkmark-layout {
  @apply absolute right-meas7 top-[10px] z-[1] hidden h-meas7 w-meas4 rotate-45 transform border-b-2 border-l-0 border-r-2 border-t-0 border-solid border-success-70;
  content: "";
  transform: rotate(45deg); /* somehow tailwind transform is not working */
}
.ic-ui-checkmark-layout.selected-card {
  @apply block;
}
.ic-ui-check-container input:disabled ~ .ic-ui-checkmark {
  @apply border border-solid border-navy-60;
}
.ic-ui-check-container.selected-card .ic-ui-checkmark {
  @apply border-[3px] border-solid border-success-70;
}
.ic-ui-check-container.selected-card .ic-ui-checkmark:after {
  @apply border-[3px] border-solid border-success-70;
}
.ic-ui-check-container.selected-card .ic-ui-checkmark .ic-ui-checkmark-box {
  @apply fill-success-30 transition duration-200 ease-in-out;
}
.ic-ui-check-container.selected-card .ic-ui-checkmark path {
  @apply fill-success-70;
}
.ic-ui-check-container.selected-card .ic-ui-checkmark circle {
  @apply fill-gray-10 stroke-success-70 transition duration-200 ease-in-out;
}
.card-title {
  @apply flex w-full flex-col items-center justify-start px-meas4 text-center font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default xl:justify-center;
}
.ic-ui-checkmark:hover:not([data-disabled="true"]) {
  @apply hover:scale-105 hover:border-[3px] hover:border-gray-10 hover:shadow-gray-10-outline-10-opacity-0.7;
}
.ic-ui-check-container.selected-card:hover .ic-ui-checkmark {
  @apply hover:scale-105 hover:border-[3px] hover:border-success-30;
}
.ic-ui-check-container.selected-card:hover .ic-ui-checkmark-box {
  @apply right-[-1px] top-[-1px];
}

.empty-card {
  @apply flex h-[197.89px] w-[198.71px] flex-col items-center justify-center rounded text-gray-30 md:h-[316.63px] md:w-[317.93px];
  background: #0e0f34;
  box-shadow:
    rgba(42, 59, 137, 0.8) 0px 1px 3px,
    rgba(42, 59, 137, 0.8) 0px 0px 1px 3px;
}
.empty-card .image-as-icon {
  @apply h-meas34 w-auto;
}
.ic-ui-card-col .icon {
  @apply m-auto h-meas34 w-meas34 cursor-pointer;
}
.ic-ui-card-col .image-as-icon {
  @apply m-auto h-meas34 w-auto cursor-pointer;
}
.checkbox-title {
  @apply flex h-meas30 w-full flex-row items-start justify-start px-meas2 pt-meas6;
}
.checkbox-title > .icon-block .icon {
  @apply mt-[6px] h-[11.25px] w-[11.25px] xl:mt-meas0 xl:h-[18.75px] xl:w-[18.75px];
}
.interested-creators-ui-type-error-message {
  @apply pb-meas16 pt-meas2 text-center font-text-regular xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1 text-error-50;
}
