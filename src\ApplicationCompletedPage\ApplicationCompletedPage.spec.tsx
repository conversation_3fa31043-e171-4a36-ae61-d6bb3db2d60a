import React from "react";
import { act, render, screen } from "@testing-library/react";
import { axe } from "jest-axe";
import { applicationCompletedLabels } from "../../test/translations/ApplicationCompleted";
import ApplicationCompletedPage from "./ApplicationCompletedPage";
import Random from "../../test/factories/Random";
import { aLocalizedDate } from "../../test/factories/LocalizedDateBuilderFactories";
import userEvent from "@testing-library/user-event";

describe("ApplicationCompleted", () => {
  const applicationCompletedPageProps = {
    labels: applicationCompletedLabels,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    submittedDate: aLocalizedDate().toString(),
    emailId: Random.email(),
    defaultGamerTag: Random.firstName(),
    onBackButtonClick: jest.fn(),
    applicationCompleteThumbnail: "https://via.placeholder.com/150"
  };

  it("shows interested creator application completed labels", () => {
    const { title, subTitle, description, backHome } = applicationCompletedLabels;

    render(<ApplicationCompletedPage {...applicationCompletedPageProps} />);

    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(subTitle)).toBeInTheDocument();
    expect(screen.getByText(description)).toBeInTheDocument();
    expect(screen.getByText(backHome)).toBeInTheDocument();
  });

  it("navigates to the correct path when the Back Home button is clicked", async () => {
    render(<ApplicationCompletedPage {...applicationCompletedPageProps} />);

    await userEvent.click(screen.getByRole("button", { name: applicationCompletedLabels.backHome }));

    expect(applicationCompletedPageProps.onBackButtonClick).toHaveBeenCalledTimes(1);
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' enabled", () => {
    it("shows interested creator application completed page labels and table", () => {
      const { programLabel, email, status, submissionDate } = applicationCompletedLabels;

      render(<ApplicationCompletedPage {...applicationCompletedPageProps} INTERESTED_CREATOR_REAPPLY_PERIOD />);

      expect(screen.getByText(programLabel)).toBeInTheDocument();
      expect(screen.getByText(email)).toBeInTheDocument();
      expect(screen.getByText(status)).toBeInTheDocument();
      expect(screen.getByText(submissionDate)).toBeInTheDocument();
      expect(screen.getByText(applicationCompletedPageProps.emailId)).toBeInTheDocument();
    });

    it("shows interested creator application completed page labels and table without EmailID", () => {
      const { programLabel, email, status, submissionDate } = applicationCompletedLabels;
      const applicationCompletedPagePropsWithoutEmailId = {
        ...applicationCompletedPageProps,
        emailId: undefined
      };

      render(
        <ApplicationCompletedPage {...applicationCompletedPagePropsWithoutEmailId} INTERESTED_CREATOR_REAPPLY_PERIOD />
      );

      expect(screen.getByText(programLabel)).toBeInTheDocument();
      expect(screen.getByText(email)).toBeInTheDocument();
      expect(screen.getByText(status)).toBeInTheDocument();
      expect(screen.getByText(submissionDate)).toBeInTheDocument();
      expect(screen.queryByText(applicationCompletedPageProps.emailId)).not.toBeInTheDocument();
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<ApplicationCompletedPage {...applicationCompletedPageProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
