import React, { FC, ReactElement } from "react";
import { Configuration, Information } from "../information/Information";
import { NextRouter } from "next/router";
import InterestedCreatorsCreatorTypePage from "./InterestedCreatorsCreatorTypePage/InterestedCreatorsCreatorTypePage";
import { <PERSON><PERSON>er<PERSON>nalytics, Close<PERSON><PERSON>ler, Dispatch, ErrorHandling, State } from "../utils";
import { InterestedCreatorsCreatorTypeFormLabels } from "./InterestedCreatorsCreatorTypeForm/InterestedCreatorsCreatorTypeForm";
import { CreatorType } from "@eait-playerexp-cn/metadata-types";

export type PageLabels = {
  interestedCreatorTitle: string;
  interestedCreatorDescription: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  unhandledError: string;
  title: string;
  close: string;
  cancel: string;
  description: string;
};

export type FormLabels = {
  cancel: string;
  next: string;
  yes: string;
  no: string;
  close: string;
};
export type InterestedCreatorsCreatorType = {
  nucleusId?: number;
  creatorTypes?: CreatorType[] | string[];
};
export type InterestedCreatorCreatorTypeProps = {
  interestedCreator: InterestedCreatorsCreatorType & Information;
  analytics: BrowserAnalytics;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  state: State;
  stableDispatch: Dispatch;
  configuration: Configuration;
  errorHandling: ErrorHandling;
  labels: Labels;
  redirectedToNextStepUrl: string;
  onClose: () => void;
  showConfirmation: boolean;
  setShowConfirmation: (value: boolean) => void;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  router: NextRouter;
  handleCancelRegistration: () => void;
  basePath?: string;
};

export type Labels = FormLabels & PageLabels & InterestedCreatorsCreatorTypeFormLabels;

const InterestedCreatorCreatorType: FC<InterestedCreatorCreatorTypeProps> = ({
  interestedCreator,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  state,
  stableDispatch,
  configuration,
  labels,
  analytics,
  handleCancelRegistration,
  errorHandling,
  redirectedToNextStepUrl,
  onClose,
  showConfirmation,
  setShowConfirmation,
  errorToast,
  router,
  basePath
}) => {
  return (
    <div className="interested-creators-ui">
      <InterestedCreatorsCreatorTypePage
        formLabels={labels}
        pageLabels={labels}
        onClose={onClose}
        showConfirmation={showConfirmation}
        interestedCreator={interestedCreator}
        stableDispatch={stableDispatch}
        errorToast={errorToast}
        unhandledError={labels.unhandledError}
        state={state}
        setShowConfirmation={setShowConfirmation}
        router={router}
        handleCancelRegistration={handleCancelRegistration}
        analytics={analytics}
        INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
        errorHandling={errorHandling}
        redirectedToNextStepUrl={redirectedToNextStepUrl}
        configuration={configuration}
        basePath={basePath}
      />
    </div>
  );
};

export default InterestedCreatorCreatorType;
