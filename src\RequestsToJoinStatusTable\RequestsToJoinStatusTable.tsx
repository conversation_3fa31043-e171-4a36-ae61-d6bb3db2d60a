import React, { FC } from "react";
import { Icon, SvgProps } from "@eait-playerexp-cn/core-ui-kit";

export type RequestsToJoinStatusTableLabels = {
  email: string;
  programLabel: string;
  status: string;
  submissionDate: string;
  programName: string;
};
export type RequestsToJoinStatusTableProps = {
  requestsToJoinStatusTableLabels: RequestsToJoinStatusTableLabels;
  emailId: string;
  INTERESTED_CREATOR_REAPPLY_PERIOD?: boolean;
  submittedDate?: string;
  statusIcon?: FC<SvgProps>;
  applicationStatus?: string;
};

const RequestsToJoinStatusTable: FC<RequestsToJoinStatusTableProps> = ({
  requestsToJoinStatusTableLabels,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  emailId,
  submittedDate,
  statusIcon,
  applicationStatus
}) => {
  const { status, programLabel, programName, email, submissionDate } = requestsToJoinStatusTableLabels;
  return (
    <table className="request-to-join-status-table" data-testid="request-to-join-status-table">
      <tbody>
        <tr className="request-to-join-status-table-header">
          <td className="request-to-join-status-table-col">{programLabel}</td>
          <td className="request-to-join-status-table-col request-to-join-status-table-emailId">{email}</td>
          {INTERESTED_CREATOR_REAPPLY_PERIOD && <td className="request-to-join-status-table-col">{submissionDate}</td>}
          <td className="request-to-join-status-table-col">{status}</td>
        </tr>
        <tr className="request-to-join-status-table-body">
          <td className="request-to-join-status-table-row request-to-join-status-table-program">{programName}</td>
          <td className="request-to-join-status-table-row request-to-join-status-table-emailId">{emailId}</td>
          {INTERESTED_CREATOR_REAPPLY_PERIOD && <td className="request-to-join-status-table-row">{submittedDate}</td>}
          <td className="request-to-join-status-table-row">
            <div className="request-to-join-status-container">
              {statusIcon && <Icon icon={statusIcon} className="request-to-join-status-icon" />}
              <div>{applicationStatus}</div>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  );
};

export default RequestsToJoinStatusTable;
