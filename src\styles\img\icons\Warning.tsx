import React, { <PERSON> } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";

const SvgToastWarning: FC<SvgProps> = (props) => {
  return (
    <svg viewBox="0 0 28 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14 1.815c-2.235 0-4.427.604-6.335 1.746A12.064 12.064 0 003.18 8.282a11.805 11.805 0 00.851 12.401l.528.738-1.474 1.057-.528-.738a13.622 13.622 0 01-.981-14.306 13.878 13.878 0 015.16-5.432A14.158 14.158 0 0114 0c2.561 0 5.074.692 7.265 2.002a13.878 13.878 0 015.159 5.433 13.622 13.622 0 01-.981 14.305l-.528.738-1.474-1.057.528-.738a11.805 11.805 0 00.852-12.401 12.064 12.064 0 00-4.486-4.721A12.347 12.347 0 0014 1.815zm.015 4.345a8.28 8.28 0 00-3.835.932 8.113 8.113 0 00-2.937 2.587 7.885 7.885 0 00-.826 7.464l.341.84-1.68.684-.34-.841A9.702 9.702 0 015.75 8.648 9.927 9.927 0 019.344 5.48a10.09 10.09 0 014.675-1.136c1.628.003 3.231.4 4.67 1.154a9.925 9.925 0 013.58 3.18 9.702 9.702 0 01.977 9.182l-.345.84-1.677-.69.344-.839a7.885 7.885 0 00-.795-7.467 8.111 8.111 0 00-2.927-2.598 8.279 8.279 0 00-3.831-.947zm-1.878 3.004a4.928 4.928 0 015.3 1.04 4.81 4.81 0 011.06 1.561 4.759 4.759 0 010 3.693 4.81 4.81 0 01-1.06 1.562c-.41.404-.89.732-1.416.969.861.231 1.68.61 2.416 1.126a7.574 7.574 0 012.804 3.674l.43 1.211H6.335l.43-1.211a7.573 7.573 0 012.803-3.674 7.715 7.715 0 012.41-1.124 4.876 4.876 0 01-1.421-.971 4.81 4.81 0 01-1.06-1.562 4.759 4.759 0 010-3.693 4.81 4.81 0 011.06-1.561c.453-.446.99-.8 1.58-1.04zm1.86 1.451c-.404 0-.803.079-1.176.23-.373.152-.71.374-.993.653-.284.28-.508.61-.66.972a2.94 2.94 0 000 2.283c.152.363.376.693.66.972.283.279.62.501.993.653a3.118 3.118 0 002.353 0c.372-.152.71-.374.993-.653.284-.279.507-.61.66-.972a2.94 2.94 0 000-2.283 2.991 2.991 0 00-.66-.972 3.066 3.066 0 00-.993-.653 3.118 3.118 0 00-1.177-.23zm.006 8.922a5.93 5.93 0 00-3.397 1.066 5.794 5.794 0 00-1.534 1.582h9.861a5.793 5.793 0 00-1.534-1.582 5.93 5.93 0 00-3.396-1.066z"
        fill="currentColor"
      />
    </svg>
  );
};

SvgToastWarning.defaultProps = {
  className: "icon"
};

export default SvgToastWarning;
