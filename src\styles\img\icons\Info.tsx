import React, { FC } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";

const SvgInfo: FC<SvgProps> = () => {
  return (
    <svg width="1rem" height="1rem" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 0C7.34784 0 4.8043 1.05357 2.92893 2.92893C1.05357 4.8043 0 7.34784 0 10C0 12.6522 1.05357 15.1957 2.92893 17.0711C4.8043 18.9464 7.34784 20 10 20C12.6522 20 15.1957 18.9464 17.0711 17.0711C18.9464 15.1957 20 12.6522 20 10C20 7.34784 18.9464 4.8043 17.0711 2.92893C15.1957 1.05357 12.6522 0 10 0ZM4.01679 4.01679C5.60364 2.42994 7.75586 1.53846 10 1.53846C12.2441 1.53846 14.3964 2.42994 15.9832 4.01679C17.5701 5.60364 18.4615 7.75586 18.4615 10C18.4615 12.2441 17.5701 14.3964 15.9832 15.9832C14.3964 17.5701 12.2441 18.4615 10 18.4615C7.75586 18.4615 5.60364 17.5701 4.01679 15.9832C2.42994 14.3964 1.53846 12.2441 1.53846 10C1.53846 7.75586 2.42994 5.60364 4.01679 4.01679ZM10.7692 3.69191V4.46114V11.2304V11.9996H9.23077V11.2304V4.46114V3.69191H10.7692ZM10 13.0769C9.79715 13.0769 9.59885 13.1371 9.43018 13.2498C9.26152 13.3625 9.13006 13.5227 9.05243 13.7101C8.9748 13.8975 8.95449 14.1037 8.99407 14.3027C9.03364 14.5016 9.13132 14.6844 9.27476 14.8278C9.4182 14.9712 9.60095 15.0689 9.79991 15.1085C9.99886 15.1481 10.2051 15.1278 10.3925 15.0501C10.5799 14.9725 10.7401 14.841 10.8528 14.6724C10.9655 14.5037 11.0256 14.3054 11.0256 14.1026C11.0256 13.8305 10.9176 13.5697 10.7252 13.3773C10.5329 13.185 10.272 13.0769 10 13.0769Z"
        fill="white"
      />
    </svg>
  );
};

SvgInfo.defaultProps = {
  className: "icon"
};

export default SvgInfo;
