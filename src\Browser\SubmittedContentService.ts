import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { ContentScanResult } from "../utils/index";
class SubmittedContentService {
  constructor(private readonly client: TraceableHttpClient) {}

  async validateContent(urls: string[], type: string): Promise<ContentScanResult> {
    const validateContentData = await this.client.post(`/api/verify-content-url?type=${type}`, { body: urls });
    return validateContentData?.data;
  }
}

export default SubmittedContentService;
