import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import InterestedCreatorsStartPage from "./InterestedCreatorsStartPage";

const meta: Meta<typeof InterestedCreatorsStartPage> = {
  title: "Component Library/Interested Creators Start Page",
  component: InterestedCreatorsStartPage,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof InterestedCreatorsStartPage>;

export const StartPage: Story = {
  args: {
    labels: {
      title: "Start your submission",
      subTitle: "Thanks for your interest in EA Support A Creator Program.",
      description: "To apply, you need to be over 18 years old and will need an ",
      descriptionSuffix: "Electronic Arts Account.",
      button: "Request to Join",
      alreadyApplied: "Already submitted? Check the status of",
      alreadyAppliedSuffix: "your application here.",
      close: "Close",
      creatorNetwork: "Creator Network",
      interestedCreatorTitle: "Interested Creator",
      unhandledError: "unhandledError"
    },
    stableDispatch: () => {},
    state: {
      exceptionCode: null,
      sessionUser: null,
      isLoading: false
    },
    startApplication: () => {},
    startThumbnail: "./img/Players-comp.png",
    unhandledError: "unhandledError",
    errorToast: () => {}
  }
};
