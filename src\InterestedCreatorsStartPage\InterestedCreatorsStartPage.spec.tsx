import React from "react";
import { act, render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import { startLabels } from "../../test/translations/Start";
import InterestedCreatorsStartPage from "./InterestedCreatorsStartPage";
import { ERROR, VALIDATION_ERROR } from "../utils/index";

describe("Interested Creators Start Page", () => {
  const interestedCreatorsStartPageProps = {
    labels: startLabels,
    stableDispatch: jest.fn(),
    errorToast: jest.fn(),
    unhandledError: "unhandledError",
    state: {},
    startApplicationRouterPath: "/start-application",
    startThumbnail: "./img/Players-comp.png",
    startApplication: jest.fn()
  };

  beforeEach(() => jest.clearAllMocks());

  it("shows start creator application labels", async () => {
    const { title, subTitle, description, descriptionSuffix, button, alreadyAppliedSuffix, alreadyApplied } =
      interestedCreatorsStartPageProps.labels;

    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} />);

    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(subTitle)).toBeInTheDocument();
    expect(screen.getByText(description)).toBeInTheDocument();
    expect(screen.getByText(descriptionSuffix)).toBeInTheDocument();
    expect(screen.getByText(button)).toBeInTheDocument();
    expect(screen.getByText(alreadyApplied)).toBeInTheDocument();
    expect(screen.getByText(alreadyAppliedSuffix)).toBeInTheDocument();
  });

  it("shows a link to create a new 'Electronic Arts Account'", async () => {
    const { descriptionSuffix } = interestedCreatorsStartPageProps.labels;

    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} />);

    expect(screen.getByText(descriptionSuffix)).toHaveAttribute("href", "/api/accounts");
  });

  it("calls startApplication when 'Request to Join' button is clicked", async () => {
    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} />);

    await userEvent.click(screen.getByText(interestedCreatorsStartPageProps.labels.button));

    expect(interestedCreatorsStartPageProps.startApplication).toHaveBeenCalledTimes(1);
  });

  it("shows link 'your request here' to check the status of an existing application", async () => {
    const { alreadyAppliedSuffix } = interestedCreatorsStartPageProps.labels;
    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} />);

    await userEvent.click(screen.getByText(alreadyAppliedSuffix));

    expect(interestedCreatorsStartPageProps.startApplication).toHaveBeenCalledTimes(1);
  });

  it("calls error Toast when isError is true", async () => {
    const { errorToast } = interestedCreatorsStartPageProps;
    const interestedCreatorsStartPagePropsWithError = {
      ...interestedCreatorsStartPageProps,
      state: { isError: true }
    };

    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPagePropsWithError} />);

    await waitFor(() => {
      expect(errorToast).toHaveBeenCalledTimes(1);
      expect(errorToast).toHaveBeenCalledWith(expect.any(Object), expect.any(Object));
    });
  });

  it("calls onToastClose with correct arguments when isError is TRUE", () => {
    const interestedCreatorsStartPagePropsWithError = {
      ...interestedCreatorsStartPageProps,
      state: { isError: true }
    };
    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPagePropsWithError} />);
    const onCloseFn = interestedCreatorsStartPageProps.errorToast.mock.calls[0][1].onClose;

    onCloseFn();

    expect(interestedCreatorsStartPageProps.stableDispatch).toHaveBeenCalledWith({ type: ERROR, data: false });
  });

  it("calls errorToast when isValidationError is not empty", () => {
    const { errorToast } = interestedCreatorsStartPageProps;
    const interestedCreatorsStartPagePropsWithError = {
      ...interestedCreatorsStartPageProps,
      state: { isValidationError: [{ message: "Validation failed" }] }
    };
    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPagePropsWithError} />);

    expect(errorToast).toHaveBeenCalledWith(
      expect.any(Object),
      expect.objectContaining({
        onClose: expect.any(Function)
      })
    );
  });

  it("calls onToastClose with correct arguments when the VALIDATION_ERROR exists", () => {
    const interestedCreatorsStartPagePropsWithError = {
      ...interestedCreatorsStartPageProps,
      state: { isValidationError: [{ message: "Validation failed" }] }
    };
    render(<InterestedCreatorsStartPage {...interestedCreatorsStartPagePropsWithError} />);
    const onCloseFn = interestedCreatorsStartPageProps.errorToast.mock.calls[0][1].onClose;

    onCloseFn();

    expect(interestedCreatorsStartPageProps.stableDispatch).toHaveBeenCalledWith({
      type: VALIDATION_ERROR,
      data: false
    });
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<InterestedCreatorsStartPage {...interestedCreatorsStartPageProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
