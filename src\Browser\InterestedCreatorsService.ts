import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { CreatorType } from "../utils";
import { Country, Language } from "@eait-playerexp-cn/metadata-types";

export type PreferredLanguage = {
  code: string;
  name: string;
};

export type ContentUrlWithoutFollowers = {
  url: string;
};
export type FranchiseType = "PRIMARY" | "SECONDARY";
export type PreferredFranchise = {
  id: string;
  type: FranchiseType;
};

export type InterestedCreator = {
  preferredFranchises?: PreferredFranchise[];
  defaultGamerTag: string;
  nucleusId: number;
  firstName?: string;
  lastName?: string;
  originEmail: string;
  dateOfBirth: string | number;
  preferredEmail?: string;
  country?: Country;
  creatorTypes?: CreatorType[] | string[];
  contentLanguages?: Language[];
  contentUrls?: ContentUrlWithoutFollowers[];
  preferredLanguage?: PreferredLanguage;
  createdDate?: string;
  canApply?: boolean;
};
class InterestedCreatorsService {
  constructor(private readonly client: TraceableHttpClient) {}

  async saveApplication(interestedCreator: InterestedCreator): Promise<void> {
    await this.client.post(`/api/applications`, { body: interestedCreator });
  }

  async saveInterestedCreatorInformation(interestedCreator: InterestedCreator): Promise<void> {
    await this.client.post(`/api/v4/interested-creators`, { body: interestedCreator });
  }
}

export default InterestedCreatorsService;
