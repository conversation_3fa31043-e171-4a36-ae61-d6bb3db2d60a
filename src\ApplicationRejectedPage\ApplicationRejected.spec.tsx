import { axe } from "jest-axe";
import { act, render, screen } from "@testing-library/react";
import { applicationRejectedLabels } from "../../test/translations/ApplicationRejected";
import ApplicationRejectedPage, { ApplicationRejectedPageProps } from "./ApplicationRejectedPage";
import { BrowserAnalytics } from "../utils";
import React from "react";
import Random from "../../test/factories/Random";

describe("ApplicationRejectedPage", () => {
  const applicationRejectedPageProps: ApplicationRejectedPageProps = {
    labels: applicationRejectedLabels,
    locale: Random.locale(),
    analytics: { checkedApplicationStatus: jest.fn() } as BrowserAnalytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: true,
    emailId: Random.email(),
    canApply: false,
    submittedDate: "Jan 3, 2025",
    reSubmitRequestDate: "Jan 13, 2025",
    redirectedToInformatioForm: "/information-form",
    logoutPath: "/api/logout",
    redirectedToMain: "/main",
    applicationRejectedThumbnail: "./img/Players-comp.png"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the labels in rejected Page", () => {
    const { submissionReviewed, submissionReviewedDescription, programName, returnToHomePage } =
      applicationRejectedLabels;

    render(<ApplicationRejectedPage {...applicationRejectedPageProps} />);

    expect(screen.getByText(submissionReviewed)).toBeInTheDocument();
    expect(screen.getByText(submissionReviewedDescription)).toBeInTheDocument();
    expect(screen.getByText(programName)).toBeInTheDocument();
    expect(screen.getByText(returnToHomePage)).toBeInTheDocument();
    expect(screen.getByRole("link", { name: returnToHomePage })).toBeInTheDocument();
  });

  it("renders labels when emailId is not defined", () => {
    const { submissionReviewed, submissionReviewedDescription, programName, returnToHomePage } =
      applicationRejectedLabels;
    const applicationRejectedPagePropsWithoutCanApply = { ...applicationRejectedPageProps, emailId: undefined };

    render(<ApplicationRejectedPage {...applicationRejectedPagePropsWithoutCanApply} />);

    expect(screen.getByText(submissionReviewed)).toBeInTheDocument();
    expect(screen.getByText(submissionReviewedDescription)).toBeInTheDocument();
    expect(screen.getByText(programName)).toBeInTheDocument();
    expect(screen.getByText(returnToHomePage)).toBeInTheDocument();
    expect(screen.getByRole("link", { name: returnToHomePage })).toBeInTheDocument();
    expect(screen.queryByText(applicationRejectedPageProps.emailId as string)).not.toBeInTheDocument();
  });

  it("calls analytics on render", () => {
    render(<ApplicationRejectedPage {...applicationRejectedPageProps} />);

    expect(applicationRejectedPageProps.analytics.checkedApplicationStatus).toHaveBeenCalledWith({
      locale: applicationRejectedPageProps.locale,
      status: "Rejected"
    });
  });

  it("renders review and resubmit button when canApply is true", () => {
    render(<ApplicationRejectedPage {...applicationRejectedPageProps} canApply={true} />);

    expect(
      screen.getByRole("link", { name: applicationRejectedPageProps.labels.reviewAndResubmit })
    ).toBeInTheDocument();
  });

  it("does not render review and resubmit button when canApply is false", () => {
    const { title, descriptionPara1, descriptionPara2 } = applicationRejectedLabels;

    render(
      <ApplicationRejectedPage {...applicationRejectedPageProps} canApply={false} reSubmitRequestDate={undefined} />
    );

    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(descriptionPara1)).toBeInTheDocument();
    expect(screen.queryByText(descriptionPara2)).not.toBeInTheDocument();
    expect(screen.queryByText(applicationRejectedPageProps.labels.reviewAndResubmit)).not.toBeInTheDocument();
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' disabled", () => {
    it("renders the labels in rejected Page", () => {
      const { title, descriptionPara1, descriptionPara2, descriptionPara3, descriptionPara4 } =
        applicationRejectedLabels;

      render(<ApplicationRejectedPage {...applicationRejectedPageProps} INTERESTED_CREATOR_REAPPLY_PERIOD={false} />);

      expect(screen.getByText(title)).toBeInTheDocument();
      expect(screen.getByText(descriptionPara1)).toBeInTheDocument();
      expect(screen.getByText(descriptionPara2)).toBeInTheDocument();
      expect(screen.getByText(descriptionPara3)).toBeInTheDocument();
      expect(screen.getByText(descriptionPara4)).toBeInTheDocument();
    });
  });

  it("is accessible", async () => {
    const { container } = render(<ApplicationRejectedPage {...applicationRejectedPageProps} />);

    const results = await act(() => axe(container));

    expect(results).toHaveNoViolations();
  });
});
