import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import ConnectAccountsInputs, { ConnectAccountsInputsProps } from "./ConnectAccountsInputs";
import { ConnectAccountLabels } from "../../../../test/translations/ConnectAccounts";
import { Configuration } from "../../Information";

describe("ConnectAccountsInputs", () => {
  const mockSetShowAddConfirmation = jest.fn();
  const configuration = { metadataClient: {}, applicationsClient: {} } as Configuration;
  const ConnectAccountsInputsProps: ConnectAccountsInputsProps = {
    labels: ConnectAccountLabels,
    setShowAddConfirmation: mockSetShowAddConfirmation,
    showAddConfirmation: false,
    stableDispatch: jest.fn(),
    errorHandling: jest.fn(),
    configuration: configuration,
    connectAccounts: []
  };

  it("shows a accounts", () => {
    render(<ConnectAccountsInputs {...ConnectAccountsInputsProps} />);

    expect(screen.getAllByRole("button").length).toBe(5);
  });

  it("shows a new window when an account card is clicked", () => {
    window.open = jest.fn();
    render(<ConnectAccountsInputs {...ConnectAccountsInputsProps} />);

    fireEvent.click(screen.getAllByRole("button")[0]);

    expect(window.open).toHaveBeenCalled();
  });
});
