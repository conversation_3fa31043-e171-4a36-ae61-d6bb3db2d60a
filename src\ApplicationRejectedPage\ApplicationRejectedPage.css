.application-rejected-thumbnail {
  @apply min-h-[199px] md:min-h-[325px] max-w-[290px] md:max-w-[473px] bg-cover bg-no-repeat;
}
.application-rejected-wrapper {
  @apply flex flex-col items-center justify-center text-white text-center w-[290px] md:w-[630px] mx-auto mt-[116px] md:mt-[224px] lg:mt-[70px];
}
.application-rejected-content-container {
  @apply w-[290px] md:w-[630px] border-b-[1px] border-[rgba(255,255,255,0.33)];
}
.application-rejected-content-container-re-apply {
  @apply border-b-0;
}
.application-rejected-title {
  @apply mb-meas16 font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 text-gray-10 md:text-white;
}
.application-rejected-descriptionPara1 {
  @apply my-meas16 text-left xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.application-rejected-description {
  @apply my-meas16 xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.application-rejected-descriptionPara2 {
  @apply text-left xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.application-rejected-descriptionPara3 {
  @apply mt-meas16 text-left xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.application-rejected-descriptionPara4 {
  @apply my-meas16 text-left xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}
.application-rejected-back-home {
  @apply border-t-[1px] border-[rgba(255,255,255,0.33)] w-full;
}
.application-rejected-back-home .btn {
  @apply my-meas16 inline-block;
}
.application-accepted-rightarrow {
  @apply ml-meas2;
}
.application-rejected-footer {
  @apply mb-meas33 text-center;
}
.application-rejected-footer .btn-secondary {
  @apply mb-meas10 w-full md:mb-meas0 md:w-[285px];
}
.application-rejected-footer .btn-primary {
  @apply w-full md:ml-meas10 md:w-[209px];
}
.application-rejected-footer {
  @apply px-meas10 md:px-meas0;
}
