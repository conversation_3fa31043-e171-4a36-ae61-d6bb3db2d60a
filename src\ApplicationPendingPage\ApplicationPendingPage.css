@media screen and (min-width: 768px) {
  .application-pending-thumbnail {
    @apply min-h-[325px] mr-[50px] h-[320px] w-[450px] bg-cover bg-no-repeat;
  }
}

@media screen and (max-width: 767px) {
  .application-pending-thumbnail {
    @apply min-h-[199px] mr-[30px] h-[145px] w-[200px] bg-cover bg-no-repeat;
  }
}

.application-pending-wrapper {
  @apply flex flex-col items-center justify-center text-white text-center w-[290px] md:w-[630px] mx-auto mt-[116px] md:mt-[224px] lg:mt-[70px];
}
.application-pending-content-container {
  @apply mx-auto text-center;
}
.application-pending-title {
  @apply mb-meas16 font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 text-gray-10;
}
.application-pending-sub-title {
  @apply font-display-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4 text-gray-10;
  word-break: break-word;
}
.application-pending-description {
  @apply my-meas16 font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large text-gray-10 md:text-center;
}
.application-pending-back-home {
  @apply border-t-[1px] border-[rgba(255,255,255,0.33)] w-full;
}
.application-pending-back-home .btn {
  @apply my-meas16 inline-block;
}
.application-pending-rightarrow {
  @apply ml-meas2;
}
.application-pending-footer {
  @apply mb-meas33 text-center;
}
.application-pending-footer .btn-secondary {
  @apply mb-meas10 w-full md:mb-meas0 md:mr-meas10 md:w-[285px];
}
.application-pending-footer .btn-primary {
  @apply w-full md:w-[209px];
}
.application-pending-footer {
  @apply px-meas10 md:px-meas0;
}
