import React from "react";
import { render, screen } from "@testing-library/react";
import { axe } from "jest-axe";
import Rejected from "../styles/img/icons/Rejected";
import { requestsToJoinStatusTableLabels } from "../../test/translations/RequestsToJoinStatusTable";
import RequestsToJoinStatusTable from "./RequestsToJoinStatusTable";
import Random from "../../test/factories/Random";

describe("RequestsToJoinStatusTable", () => {
  const requestsToJoinStatusTableProps = {
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    submittedDate: "Jan 02, 2024",
    emailId: Random.email(),
    applicationStatus: "Closed",
    statusIcon: Rejected,
    requestsToJoinStatusTableLabels: requestsToJoinStatusTableLabels
  };

  it("shows application status table", async () => {
    render(<RequestsToJoinStatusTable {...requestsToJoinStatusTableProps} />);

    expect(
      screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.programLabel)
    ).toBeInTheDocument();
    expect(screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.email)).toBeInTheDocument();
    expect(screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.status)).toBeInTheDocument();
    expect(
      screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.programName)
    ).toBeInTheDocument();
    expect(screen.getByText(requestsToJoinStatusTableProps.emailId)).toBeInTheDocument();
    expect(screen.getByText(requestsToJoinStatusTableProps.applicationStatus)).toBeInTheDocument();
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' enabled", () => {
    it("shows application status table", async () => {
      render(<RequestsToJoinStatusTable {...requestsToJoinStatusTableProps} INTERESTED_CREATOR_REAPPLY_PERIOD />);

      expect(
        screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.programLabel)
      ).toBeInTheDocument();
      expect(
        screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.email)
      ).toBeInTheDocument();
      expect(
        screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.status)
      ).toBeInTheDocument();
      expect(
        screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.submissionDate)
      ).toBeInTheDocument();
      expect(
        screen.getByText(requestsToJoinStatusTableProps.requestsToJoinStatusTableLabels.programName)
      ).toBeInTheDocument();
      expect(screen.getByText(requestsToJoinStatusTableProps.emailId)).toBeInTheDocument();
      expect(screen.getByText(requestsToJoinStatusTableProps.applicationStatus)).toBeInTheDocument();
      expect(screen.getByText(requestsToJoinStatusTableProps.submittedDate)).toBeInTheDocument();
    });
  });

  it("is accessible", async () => {
    const { container } = render(<RequestsToJoinStatusTable {...requestsToJoinStatusTableProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
