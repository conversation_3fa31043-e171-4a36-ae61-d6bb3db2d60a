import React from "react";
import { render, screen, waitFor, within } from "@testing-library/react";
import {
  clearDateFor,
  clearValueFor,
  clearValueForUrl,
  enterDateFor,
  enterValueFor,
  selectMultipleOptions,
  selectOption
} from "../../../test/helpers/Forms";
import { NextRouter } from "next/router";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import "next/config";
import { interestedCreatorInformationFormLabels } from "../../../test/translations/InterestedCreatorInformationForm";
import userEvent from "@testing-library/user-event";
import CreatorForm from "../../utils/CreatorForm";
import { informationLabels } from "../../../test/translations/Information";
import { communicationPreferencesLabels } from "../../../test/translations/CommunicationPreferences";
import { aLocalizedDate } from "../../../test/factories/LocalizedDateBuilderFactories";
import { BrowserAnalytics, ConnectedAccount } from "../../utils";
import { Configuration, Rules } from "../Information";
import InterestedCreatorInformationForm, {
  InterestedCreatorInformationFormProps
} from "./InterestedCreatorInformationForm";
import SubmittedContentService from "../../Browser/SubmittedContentService";
import InterestedCreatorsService from "../../Browser/InterestedCreatorsService";
import { ConnectAccountLabels } from "../../../test/translations/ConnectAccounts";
import Random from "../../../test/factories/Random";

jest.mock("../../Browser/InterestedCreatorsService");
jest.mock("../../Browser/SubmittedContentService");

describe("InterestedCreatorInformationForm", () => {
  const submittedContentService = { validateContent: jest.fn() } as unknown as SubmittedContentService;
  const interestedCreatorsService = { saveApplication: jest.fn() } as unknown as InterestedCreatorsService;
  const { formLabels } = interestedCreatorInformationFormLabels;
  const onClose = jest.fn();
  const locale = "en-us";
  const allRules = {
    ...CreatorForm.rules(informationLabels),
    ...CreatorForm.communicationRules(communicationPreferencesLabels)
  };
  const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
  const { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url, followers } = allRules;
  const rules: Rules = { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url, followers };
  const stableDispatch = jest.fn();
  const analytics = {} as unknown as BrowserAnalytics;
  const router = { locale, push: jest.fn() } as unknown as NextRouter;
  const locales = [
    {
      value: "en_US",
      label: "English",
      id: "a0dK000000dacrfIAA"
    },
    {
      value: "es_ES",
      label: "Español",
      id: "a0dK000000dzdPtIAI"
    },
    {
      value: "ja_JP",
      label: "日本語",
      id: "a0dK000000dade5IAA"
    }
  ];
  const creatorFormValues = {
    firstName: "Jane",
    lastName: "Doe",
    country: { name: "Canada", label: "Canada", value: "CA" },
    contentUrls: [{ url: "https://www.google.com", followers: "" }],
    contentLanguages: [
      {
        value: "en",
        label: "English",
        id: "a0LK0000008epjzMAA"
      }
    ]
  };
  const interestedCreator = {
    nucleusId: 1234567,
    defaultGamerTag: "RiffleShooter",
    originEmail: "<EMAIL>",
    dateOfBirth: dateOfBirthAfter18years,
    contentUrls: [
      {
        url: "https://www.google.com",
        followers: "20"
      }
    ],
    preferredLanguage: {
      code: "en_US",
      name: "English"
    }
  };

  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  const accounts = [
    {
      name: Random.firstName(),
      disconnected: false,
      username: Random.string(),
      id: Random.uuid(),
      type: "FACEBOOK",
      uri: Random.url(),
      thumbnail: Random.imageUrl(),
      isExpired: true,
      accountId: Random.uuid()
    }
  ];
  const configuration = { metadataClient: {}, applicationsClient: {} } as Configuration;
  const interestedCreatorInformationProps: InterestedCreatorInformationFormProps = {
    redirectedToNextStepUrl: "/interested-creators/creator-types",
    state: {
      onboardingSteps: steps
    },
    errorHandling: jest.fn(),
    configuration: configuration,
    formLabels,
    rules,
    locale,
    router,
    stableDispatch,
    onClose,
    analytics,
    countries: [
      { name: "Canada", label: "Canada", value: "CA" },
      { name: "Mexico", label: "Mexico", value: "MX" }
    ],
    locales,
    languages: [
      {
        value: "en",
        label: "English"
      }
    ],
    interestedCreator,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    showAddConfirmation: false,
    setShowAddConfirmation: jest.fn(),
    connectAccountLabels: ConnectAccountLabels,
    accountToRemove: "",
    setAccountToRemove: jest.fn(),
    showRemoveAccountModal: false,
    setShowRemoveAccountModal: jest.fn(),
    accounts: [],
    pages: [],
    connectAccounts: []
  };
  const interestedCreatorInformationPropsWithFlagEnabled = {
    ...interestedCreatorInformationProps,
    rules
  };
  const scanResult = {
    url: interestedCreator.contentUrls[0].url,
    isSecure: true
  };
  const urlScanResult = { results: [scanResult] };

  beforeEach(() => {
    jest.clearAllMocks();
    (SubmittedContentService as jest.Mock).mockReturnValue(submittedContentService);
    (InterestedCreatorsService as jest.Mock).mockReturnValue(interestedCreatorsService);
  });

  it("enables next button on populating required values", async () => {
    const interestedCreatorWithNoValues = {
      nucleusId: 0,
      defaultGamerTag: "",
      originEmail: "",
      dateOfBirth: undefined,
      contentUrls: [{ url: "", followers: "" }]
    };
    const input = {
      firstName: "Jane",
      lastName: "Doe",
      dateOfBirth: `"${dateOfBirthAfter18years}"`,
      country: "Mexico",
      originEmail: "<EMAIL>",
      preferredEmail: "<EMAIL>",
      contentLanguage: "English",
      submittedUrl: "https://www.google.com"
    };
    const { container } = render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreatorWithNoValues}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    const nextButton = screen.getByRole("button", { name: "Next" });
    expect(nextButton).toBeDisabled();

    // Populate required fields
    await enterValueFor(/^First Name/i, input.firstName);
    await enterValueFor(/^Last Name/i, input.lastName);
    await enterValueFor(/^Preferred Email/i, input.preferredEmail);
    await clearDateFor(/Date of Birth/i);
    await enterDateFor(/Date of Birth/i, input.dateOfBirth);
    // Select country
    await selectOption({ option: input.country, container, label: "Country/Region" });
    // Select content languages
    await selectMultipleOptions([input.contentLanguage]);
    await enterValueFor(/^Website URL/i, input.submittedUrl);

    await waitFor(() => expect(nextButton).toBeEnabled());
  }, 15000);

  it("disables next button on populating date of birth under 18 years", async () => {
    const dateOfBirthBefore18Years = aLocalizedDate().minusYears(18).plusDays(2).build().formatWithEpoch("MM/DD/YYYY");
    const interestedCreatorWithNoValues = {
      nucleusId: 0,
      defaultGamerTag: "",
      originEmail: "",
      dateOfBirth: undefined,
      contentUrls: [{ url: "", followers: "" }]
    };
    const input = {
      firstName: "Jane",
      lastName: "Doe",
      dateOfBirth: dateOfBirthAfter18years,
      country: "Mexico",
      originEmail: "<EMAIL>",
      preferredEmail: "<EMAIL>",
      contentLanguage: "English",
      submittedUrl: "https://www.google.com"
    };
    const { container } = render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreatorWithNoValues}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    const nextButton = screen.getByRole("button", { name: "Next" });
    expect(nextButton).toBeDisabled();
    // Populate required fields
    await enterValueFor(/^First Name/i, input.firstName);
    await enterValueFor(/^Last Name/i, input.lastName);
    await enterValueFor(/^Preferred Email/i, input.preferredEmail);
    await clearDateFor(/Date of Birth/i);
    await enterDateFor(/Date of Birth/i, input.dateOfBirth);
    // Select country
    await selectOption({ option: input.country, container, label: "Country/Region" });
    // Select content languages
    await selectMultipleOptions([input.contentLanguage]);
    await enterValueFor(/^Website URL/i, input.submittedUrl);
    await waitFor(() => expect(nextButton).toBeEnabled());
    await clearDateFor(/Date of Birth/i);

    await enterDateFor(/Date of Birth/i, dateOfBirthBefore18Years);

    expect(await screen.findByText(formLabels.ageMustBe18OrOlder)).toBeInTheDocument();
    await waitFor(() => expect(nextButton).toBeDisabled());
  }, 15000);

  it("is populated with interested creator information", async () => {
    const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("MM/DD/YYYY");
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      dateOfBirth: dateOfBirthAfter18years
    };

    render(
      <InterestedCreatorInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    await waitFor(() => {
      expect(screen.getByLabelText(/^Website URL/i)).toHaveValue(interestedCreator.contentUrls[0].url);
      expect(screen.getByLabelText(/^First Name/i)).toHaveValue(interestedCreator.firstName);
      expect(screen.getByLabelText(/^Last Name/i)).toHaveValue(interestedCreator.lastName);
      expect(screen.getByLabelText(/^Preferred Email/i)).toHaveValue(interestedCreator.originEmail);
      expect(screen.getByPlaceholderText(/Date of Birth/i)).toHaveValue(interestedCreator.dateOfBirth);
      expect(screen.queryByText(interestedCreator.country.label)).toBeInTheDocument();
    });
  });

  it("shows error messages when mandatory fields are cleared ", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };
    render(
      <InterestedCreatorInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    await clearValueFor(/^First Name/i);
    await clearValueFor(/^Last Name/i);
    await clearValueFor(/^Preferred Email/i);
    await clearValueFor(/Date of Birth/i);
    await clearValueForUrl(/^Website URL/i);

    await waitFor(() => {
      expect(screen.getByText(/First Name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Last Name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Preferred Email Address is required/i)).toBeInTheDocument();
      expect(screen.getByText(/Date of Birth is required/i)).toBeInTheDocument();
    });
  });

  it("allows adding or removing more urls  ", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      contentUrls: [{ url: "", followers: "" }],
      preferredEmail: ""
    };
    render(
      <InterestedCreatorInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    await userEvent.click(screen.getByRole("button", { name: "Add another URL" }));
    await waitFor(() => expect(screen.getAllByText(/^Website URL/i)).toHaveLength(2));

    await userEvent.click(screen.getByLabelText("Remove"));
    await waitFor(() => expect(screen.getAllByText(/^Website URL/i)).toHaveLength(1));
  });

  it("calls its submit handler and redirect to the next page", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };
    (submittedContentService.validateContent as jest.Mock).mockResolvedValue(urlScanResult);
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    await selectOption({ option: "English", container, label: "Language" });
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
      expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledTimes(1);
      expect(submittedContentService.validateContent).toHaveBeenCalledTimes(1);
      expect(submittedContentService.validateContent).toHaveBeenCalledWith(
        [interestedCreator.contentUrls[0].url],
        "INTERESTED_CREATORS"
      );
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledWith({
        ...interestedCreator,
        country: { ...interestedCreator.country },
        preferredEmail: "<EMAIL>",
        preferredLanguage: {
          code: "en_US",
          name: "English"
        }
      });
    });
  });

  it("restrict to adding only 10 URLs", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      contentUrls: [{ url: "", followers: "" }]
    };
    render(
      <InterestedCreatorInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    await addAnotherUrl(2);
    await addAnotherUrl(3);
    await addAnotherUrl(4);
    await addAnotherUrl(5);
    await addAnotherUrl(6);
    await addAnotherUrl(7);
    await addAnotherUrl(8);
    await addAnotherUrl(9);
    await addAnotherUrl(10);

    await waitFor(() => expect(screen.queryByRole("button", { name: "Add another URL" })).not.toBeInTheDocument());
  }, 10000);

  it("shows error message if a duplicate URL is entered", async () => {
    const duplicateUrl = "https://www.google.com";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [{ url: "", followers: "" }]
    };
    render(
      <InterestedCreatorInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );
    // Enter valid URL
    await enterValueFor(/^Website URL/i, duplicateUrl);
    // Add one more URL
    await userEvent.click(screen.getByRole("button", { name: "Add another URL" }));
    await waitFor(() => expect(screen.getAllByLabelText(/^Website URL/i)).toHaveLength(2));

    // provide duplicate URL
    await userEvent.type(screen.getAllByLabelText(/^Website URL/i)[1], duplicateUrl);

    await waitFor(() => {
      expect(screen.queryByText(/Duplicate URLs not allowed/i)).toBeInTheDocument();
    });
  });

  it("shows error message when API content scanning failed and doesn't redirect to next page", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };
    const urlScanError = { ...urlScanResult, results: [{ ...scanResult, isSecure: false }] };
    (submittedContentService.validateContent as jest.Mock).mockResolvedValue(urlScanError);
    render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreator}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(submittedContentService.validateContent).toHaveBeenCalledWith(
        [interestedCreator.contentUrls[0].url],
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(/You cannot submit content from this website/i)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(submittedContentService.validateContent).toHaveBeenCalledTimes(1);
    });
  });

  it("shows error message when API content scanning failed for one url and doesn't redirect to next page", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [
        { url: "https://ea.com", followers: "" },
        { url: "https://test.com", followers: "" }
      ]
    };
    const urlScanError = { ...urlScanResult, results: [scanResult, { url: "https://test.com", isSecure: false }] };
    (submittedContentService.validateContent as jest.Mock).mockResolvedValue(urlScanError);
    render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreator}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(submittedContentService.validateContent).toHaveBeenCalledWith(
        [interestedCreator.contentUrls[0].url, interestedCreator.contentUrls[1].url],
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(/You cannot submit content from this website/i)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(submittedContentService.validateContent).toHaveBeenCalledTimes(1);
    });
  });

  it("saves interested creator information", async () => {
    const updatedPreferredEmail = "<EMAIL>";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      dateOfBirth: dateOfBirthAfter18years,
      preferredEmail: updatedPreferredEmail
    };
    (submittedContentService.validateContent as jest.Mock).mockResolvedValue(urlScanResult);
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationProps}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    // Clear preferred email
    await clearValueFor(/^Preferred Email for Communication/i);
    // Modify preferred email
    await enterValueFor(/^Preferred Email for Communication/i, updatedPreferredEmail);
    await selectOption({ option: "English", container, label: "Language" });
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(async () => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      // Verify update application call
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledTimes(1);
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledWith(interestedCreator);
      //Validate the preferredEmail is same as updatedPreferredEmail in the response
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
      // Verify content url call
      expect(submittedContentService.validateContent).toHaveBeenCalledTimes(1);
      expect(submittedContentService.validateContent).toHaveBeenCalledWith(
        [interestedCreator.contentUrls[0].url],
        "INTERESTED_CREATORS"
      );
      expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
    });
  });

  it("displays 'Language for Communication' section", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };

    render(
      <InterestedCreatorInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );

    expect(await screen.findByText("Language for Communication")).toBeInTheDocument();
    expect(await screen.findByLabelText("Language")).toBeInTheDocument();
  });

  it("enables next button on populating required values without additional links", async () => {
    const interestedCreatorWithNoValues = {
      nucleusId: 0,
      defaultGamerTag: "",
      originEmail: "",
      dateOfBirth: undefined,
      contentUrls: [{ url: "", followers: "" }],
      preferredEmail: "",
      contentLanguage: "",
      firstName: "",
      lastName: ""
    };
    const input = {
      firstName: "Jane",
      lastName: "Doe",
      dateOfBirth: `"${dateOfBirthAfter18years}"`,
      country: "Mexico",
      originEmail: "<EMAIL>",
      preferredEmail: "<EMAIL>",
      contentLanguage: "English",
      preferredLanguage: "English",
      submittedUrl: ""
    };
    const { container } = render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreatorWithNoValues}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    await clearDateFor(/Date of Birth/i);
    const nextButton = screen.getByRole("button", { name: "Next" });
    expect(nextButton).toBeDisabled();
    // Populate required fields
    await enterValueFor(/^First Name/i, input.firstName);
    await enterValueFor(/^Last Name/i, input.lastName);
    await enterValueFor(/^Preferred Email/i, input.preferredEmail);
    await enterDateFor(/Date of Birth/i, input.dateOfBirth);
    // Select country
    await selectOption({ option: input.country, container, label: "Country/Region" });
    // Select content languages
    await selectMultipleOptions([input.contentLanguage]);
    //Select preferred language
    await waitFor(
      async () => {
        await selectOption({ option: input.preferredLanguage, container, label: "Language" });
      },
      { timeout: 1_400 }
    );

    await waitFor(() => expect(nextButton).toBeEnabled());
  }, 12_000);

  it("occupies full width for the first website url", async () => {
    render(<InterestedCreatorInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} />);

    await waitFor(() => {
      const { getAllByRole } = within(screen.getByRole("list"));

      expect(getAllByRole("listitem")[0]).toHaveClass("interested-creators-ui-additional-content-url-without-delete");
    });
  });

  it("saves interested creator information with additional links entered and validated the url", async () => {
    const updatedPreferredEmail = "<EMAIL>";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      dateOfBirth: dateOfBirthAfter18years,
      preferredEmail: updatedPreferredEmail
    };
    (submittedContentService.validateContent as jest.Mock).mockResolvedValue(urlScanResult);
    (interestedCreatorsService.saveApplication as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    // Clear preferred email
    await clearValueFor(/^Preferred Email for Communication/i);
    // Modify preferred email
    await enterValueFor(/^Preferred Email for Communication/i, updatedPreferredEmail);
    //Select preferred language
    await selectOption({ option: "English", container, label: "Language" });
    await userEvent.clear(await screen.findByLabelText(/^Website URL/i));
    await userEvent.type(await screen.findByLabelText(/^Website URL/i), "https://www.google.com");
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(submittedContentService.validateContent).toHaveBeenCalledTimes(1);
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledTimes(1);
      // Verify content url call
      expect(submittedContentService.validateContent).toHaveBeenCalledWith(
        [interestedCreator.contentUrls[0].url],
        "INTERESTED_CREATORS"
      );
      // Verify update application call
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledWith(interestedCreator);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
    });
  });

  it("saves interested creator information with additional links not entered and not validated the url", async () => {
    const updatedPreferredEmail = "<EMAIL>";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      preferredEmail: updatedPreferredEmail,
      dateOfBirth: dateOfBirthAfter18years,
      contentUrls: [{ url: "https://", followers: "" }] // whenever user clears the url(as per current implementation, `https://` added all the time), the value will be `https://` and while sending to api, we are passing it as empty string.
    };
    (interestedCreatorsService.saveApplication as jest.Mock).mockImplementation(() => Promise.resolve());
    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    // Clear preferred email
    await clearValueFor(/^Preferred Email for Communication/i);
    // Modify preferred email
    await enterValueFor(/^Preferred Email for Communication/i, updatedPreferredEmail);
    //Select preferred language
    await selectOption({ option: "English", container, label: "Language" });
    await userEvent.clear(await screen.findByLabelText(/^Website URL/i));
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(router.push).toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledTimes(1);
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledWith(interestedCreator);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledTimes(1);
      expect(analytics.continuedCreatorApplication).toHaveBeenCalledWith({
        finalStep: false,
        locale: "en-us",
        page: "/"
      });
    });
  });

  it("shows error message when API content scanning failed, when additional links we entered is invalid", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues
    };
    const urlScanError = { ...urlScanResult, results: [{ ...scanResult, isSecure: false }] };
    (submittedContentService.validateContent as jest.Mock).mockResolvedValue(urlScanError);
    render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(submittedContentService.validateContent).toHaveBeenCalledWith(
        [interestedCreator.contentUrls[0].url],
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(/You cannot submit content from this website/i)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(submittedContentService.validateContent).toHaveBeenCalledTimes(1);
    });
  });

  it("shows error message when content scanning API failed with error code 422", async () => {
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [{ url: "https://-creatornetwork.ea.com", followers: "" }]
    };
    const urlScanError = {
      status: 422,
      code: "validate-content-urls-invalid-input",
      errors: {
        "urls[0]": ["value must be a valid URL. Invalid domain -creatornetwork.ea.com"]
      }
    };
    (submittedContentService.validateContent as jest.Mock).mockRejectedValue({
      response: {
        ...urlScanError,
        data: urlScanError
      }
    });
    render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(submittedContentService.validateContent).toHaveBeenCalledWith(
        [interestedCreator.contentUrls[0].url],
        "INTERESTED_CREATORS"
      );
      expect(screen.getByText(formLabels.invalidUrl)).toBeInTheDocument();
      expect(router.push).not.toHaveBeenCalledWith("/interested-creators/creator-types");
      expect(submittedContentService.validateContent).toHaveBeenCalledTimes(1);
    });
  });

  it("shows the additional links section", async () => {
    render(<InterestedCreatorInformationForm {...interestedCreatorInformationPropsWithFlagEnabled} />);

    expect(await screen.findByRole("heading", { name: "Additional Content and Website Links" })).toBeInTheDocument();
    expect(await screen.findByText(formLabels.additionalContentAndWebsiteDescription)).toBeInTheDocument();
    expect(await screen.findByLabelText(/^Website URL/i)).toBeInTheDocument();
    expect(await screen.findByRole("button", { name: "Add another URL" })).toBeInTheDocument();
  });

  it("handles API error while submit information form with content URL's", async () => {
    const updatedPreferredEmail = "<EMAIL>";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      preferredEmail: updatedPreferredEmail,
      contentUrls: [{ url: "https://", followers: "" }] // whenever user clears the url(as per current implementation, `https://` added all the time), the value will be `https://` and while sending to api, we are passing it as empty string.
    };
    (interestedCreatorsService.saveApplication as jest.Mock).mockRejectedValue({
      data: {
        statusCode: 401,
        message: "Unauthorized"
      }
    });

    const analytics = { continuedCreatorApplication: jest.fn() } as unknown as BrowserAnalytics;
    const { container } = render(
      <InterestedCreatorInformationForm
        {...interestedCreatorInformationPropsWithFlagEnabled}
        interestedCreator={interestedCreator}
        analytics={analytics}
        accounts={accounts as ConnectedAccount[]}
      />
    );
    // Clear preferred email
    await clearValueFor(/^Preferred Email for Communication/i);
    // Modify preferred email
    await enterValueFor(/^Preferred Email for Communication/i, updatedPreferredEmail);
    //Select preferred language
    await selectOption({ option: "English", container, label: "Language" });
    await userEvent.clear(await screen.findByLabelText(/^Website URL/i));
    const nextButton = screen.getByRole("button", { name: /Next/i });
    await waitFor(() => expect(nextButton).toBeEnabled());

    await userEvent.click(nextButton);

    await waitFor(() => {
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledTimes(1);
      expect(interestedCreatorsService.saveApplication).toHaveBeenCalledWith(interestedCreator);
    });
  });

  it("displays error message for duplicate URL with space", async () => {
    const duplicateUrl = "https://www.google.com";
    const duplicateUrlwithSpace = "https://www.google.com ";
    const interestedCreator = {
      ...interestedCreatorInformationProps.interestedCreator,
      ...creatorFormValues,
      contentUrls: [{ url: "", followers: "" }]
    };
    render(
      <InterestedCreatorInformationForm {...interestedCreatorInformationProps} interestedCreator={interestedCreator} />
    );
    // Enter valid URL
    await enterValueFor(/^Website URL/i, duplicateUrl);
    // Add one more URL
    await userEvent.click(screen.getByRole("button", { name: "Add another URL" }));
    await waitFor(() => expect(screen.getAllByLabelText(/^Website URL/i)).toHaveLength(2));

    // provide duplicate URL with space
    await userEvent.type(screen.getAllByLabelText(/^Website URL/i)[1], duplicateUrlwithSpace);

    await waitFor(() => {
      expect(screen.queryByText(formLabels.duplicateUrl)).toBeInTheDocument();
    });
  });

  const addAnotherUrl = async (count: number) => {
    await userEvent.click(screen.getByRole("button", { name: "Add another URL" }));
    await waitFor(() => expect(screen.getAllByText(/^Website URL/i)).toHaveLength(count));
  };
});
