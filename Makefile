.PHONY: help
help: ## Show help
	@echo Please specify a build target. The choices are:
	@grep -E '^[0-9a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

.PHONY: bootstrap
bootstrap: ## Create default .env file and install dependencies
	@echo "Creating default .env file..."
	@cp .env.dist .env
	@echo "Installing project dependencies..."
	@npm ci --prefer-offline

.PHONY: format
format: ## Format code
	@echo "Applying linting rules..."
	@npm run lint
	@echo "Applying coding standard formatting..."
	@npm run format

.PHONY: lint
lint: ## Check code format and validity
	@echo "Checking linting rules..."
	@npm run lint:check
	@echo "Checking coding standards..."
	@npm run format:check
	@echo "Checking types.."
	@npm run test:types

.PHONY: check/ci
check/ci: # Run test suite with coverage
	@npm run test:coverage

.PHONY: mutation
mutation: # Run mutation test suite
	@npm run test:mutation

.PHONY: publish/unstable
PACKAGE_VERSION := $(shell eval echo $$\(npm pkg get version\))'-dev.'$(shell date +%s)
publish/unstable: ## Publish unstable version of an npm package
	@npm run build
	@npm --no-git-tag-version version $(PACKAGE_VERSION)
	@npm publish

.PHONY: docs
docs: ## Start documentation container
	@echo "Starting container for documentation..."
	@docker-compose up -d docs
