import React, { <PERSON> } from "react";
import { Button } from "@eait-playerexp-cn/core-ui-kit";

export type Labels = {
  title: string;
  subTitle: string;
  close: string;
  bannerImageLabel: string;
};
export type AgeRestrictionPageProps = {
  labels: Labels;
  onClose: () => void;
  ageRestrictionBannerImage: string;
};

const AgeRestrictionPage: FC<AgeRestrictionPageProps> = ({ labels, onClose, ageRestrictionBannerImage }) => {
  const { title, subTitle, close } = labels;

  return (
    <div className="age-restriction-wrapper">
      <img className="age-restriction-banner-image" src={ageRestrictionBannerImage} alt="" />
      <div className="age-restriction-content-container">
        <h3 className="age-restriction-title">{title}</h3>
        <div className="age-restriction-body">{subTitle}</div>
      </div>
      <div className="age-restriction-back-home">
        <Button variant="primary" size="md" onClick={onClose}>
          {close}
        </Button>
      </div>
    </div>
  );
};

export default AgeRestrictionPage;
