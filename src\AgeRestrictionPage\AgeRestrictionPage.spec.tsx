import React from "react";
import { render, screen } from "@testing-library/react";
import { axe } from "jest-axe";
import userEvent from "@testing-library/user-event";
import AgeRestrictionPage, { AgeRestrictionPageProps } from "./AgeRestrictionPage";
import { ageRestrictionLabels } from "../../test/translations/AgeRestriction";
import Random from "../../test/factories/Random";

describe("AgeRestrictionPage", () => {
  const ageRestrictionPageProps: AgeRestrictionPageProps = {
    labels: ageRestrictionLabels,
    onClose: jest.fn(),
    ageRestrictionBannerImage: Random.imageUrl()
  };

  beforeEach(() => jest.clearAllMocks());

  it("shows age restriction page with correct labels", () => {
    const { title, subTitle, close } = ageRestrictionLabels;
    render(<AgeRestrictionPage {...ageRestrictionPageProps} />);

    expect(screen.getByText(title)).toBeInTheDocument();
    expect(screen.getByText(subTitle)).toBeInTheDocument();
    expect(screen.getByText(close)).toBeInTheDocument();
  });

  it("redirects to log out page on clicking close button", async () => {
    render(<AgeRestrictionPage {...ageRestrictionPageProps} />);

    await userEvent.click(await screen.findByRole("button", { name: ageRestrictionLabels.close }));

    expect(ageRestrictionPageProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("is accessible", async () => {
    const { container } = render(<AgeRestrictionPage {...ageRestrictionPageProps} />);

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
