import React from "react";
import { act, render } from "@testing-library/react";
import { ConnectAccountsForm, ConnectAccountsFormProps } from "./ConnectAccountsForm";
import Random from "../../../../test/factories/Random";
import { ConnectAccountLabels } from "../../../../test/translations/ConnectAccounts";
import { axe } from "jest-axe";
import { Configuration } from "../../Information";

describe("ConnectAccountsForm", () => {
  const connectAccountsFormProps: ConnectAccountsFormProps = {
    labels: ConnectAccountLabels,
    setAccountToRemove: jest.fn(),
    setShowAddConfirmation: jest.fn(),
    accountToRemove: "",
    showAddConfirmation: false,
    accounts: [
      {
        name: Random.firstName(),
        disconnected: false,
        username: Random.string(),
        id: Random.uuid(),
        type: "FACEBOOK",
        uri: Random.url(),
        thumbnail: Random.imageUrl(),
        isExpired: true,
        accountId: Random.uuid()
      }
    ],
    setShowRemoveAccountModal: jest.fn(),
    showRemoveAccountModal: false,
    state: {},
    errorHandling: jest.fn(),
    stableDispatch: jest.fn(),
    configuration: { metadataClient: {}, applicationsClient: {} } as Configuration,
    connectAccounts: []
  };

  it("is accessible", async () => {
    let results;
    const { container } = render(<ConnectAccountsForm {...connectAccountsFormProps} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
