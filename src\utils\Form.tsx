// @ts-nocheck

import React, { memo, ReactNode } from "react";
import { FieldValues, FormProvider, useForm, ValidationMode } from "react-hook-form";

export type FormProps = {
  children: ReactNode;
  defaultValues?: unknown;
  mode?: keyof ValidationMode;
  onSubmit: (data: FieldValues) => void;
  key?: string;
  revalidate?: string;
};

const Form = memo(function Form({
  children,
  defaultValues = {},
  mode = "onSubmit",
  onSubmit = () => {},
  ...props
}: FormProps) {
  const methods = useForm({ mode, defaultValues, ...props });
  const { handleSubmit } = methods;

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>{children}</form>
    </FormProvider>
  );
});

export default Form;
