import type { StorybookConfig } from "@storybook/react-webpack5";

const config: StorybookConfig = {
  stories: ["../src/**/*.stories.@(tsx)"],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    "@storybook/addon-interactions",
    "@storybook/preset-create-react-app",
    "@storybook/addon-styling-webpack"
  ],
  framework: { name: "@storybook/nextjs", options: {} },
  docs: { autodocs: true },
  core: { builder: "@storybook/builder-webpack5", disableTelemetry: true },
  staticDirs: ["../public", "../src/styles"]
};

export default config;
