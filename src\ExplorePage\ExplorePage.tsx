import React, { memo } from "react";
import Link from "next/link";

export type PageProps = {
  image: string;
  title: string;
  actionLabel: string;
  href: string;
  key?: string | number;
};

export type ExplorePagesProps = {
  title: string;
  explorePages: Array<PageProps>;
};

const Explore = memo(function Explore({ image, title, actionLabel, href }: PageProps) {
  return (
    <div className="explore-item">
      <div className="explore-thumbnail">
        <img src={image} alt="" />
      </div>
      <h4 className="explore-label">{title}</h4>
      <div className="explore-button">
        <Link href={href} className="btn btn-secondary btn-md">
          {actionLabel}
        </Link>
      </div>
    </div>
  );
});

const ExplorePages = memo(function ExplorePages({ title, explorePages }: ExplorePagesProps) {
  return (
    <div className="explore-outer-container">
      <div className="explore-container">
        <h3 className="explore-title">{title}</h3>
        <div className="explore-item-container">
          {explorePages?.map((page) => {
            return (
              <Explore
                key={page.title}
                title={page.title}
                image={page.image}
                actionLabel={page.actionLabel}
                href={page.href}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
});

export default ExplorePages;
