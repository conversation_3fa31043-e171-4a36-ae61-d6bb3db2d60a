.complete-wrapper {
  @apply flex flex-col items-center justify-center text-white text-center w-[290px] md:w-[630px] mx-auto mt-[116px] md:mt-[224px] lg:mt-[70px];
}
.complete-thumbnail-container {
  @apply min-h-[199px] md:min-h-[325px] w-[290px] md:w-[630px] border-b-[1px] border-[rgba(255,255,255,0.33)];
}
.reapply {
  @apply mt-[116px] md:mt-[224px] lg:mt-[70px];
}
.complete-thumbnail {
  @apply max-w-[290px] md:max-w-[473px] bg-cover bg-no-repeat mx-auto;
}
.complete-content-container {
  @apply mx-auto text-center;
}
.complete-title {
  @apply mb-meas16 font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 text-gray-10 md:text-white;
}
.complete-sub-title {
  @apply font-display-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4 text-gray-10;
  word-break: break-word;
}
.complete-body {
  @apply my-meas16 font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large text-gray-10 md:text-center;
}
.complete-back-home {
  @apply border-t-[1px] border-[rgba(255,255,255,0.33)] w-full;
}
.complete-back-home .btn {
  @apply my-meas16;
}
.complete-content-container-re-apply {
  @apply border-b-0;
}
