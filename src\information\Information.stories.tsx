import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import Information from "./Information";
import { NextRouter } from "next/router";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { ConnectAccountLabels } from "../../test/translations/ConnectAccounts";
import { facebookIcon, instagramIcon, tiktokIcon, twitchIcon, youTubeIcon } from "@eait-playerexp-cn/core-ui-kit";

const meta: Meta<typeof Information> = {
  title: "Component Library/Information Page",
  component: Information,
  parameters: {
    layout: "fullscreen"
  },
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof Information>;

const dispatch = (): void => {};

export const InformationPage: Story = {
  args: {
    redirectedToNextStepUrl: "/information/creatorTypes",
    interestedCreator: {
      defaultGamerTag: "Gamer123",
      nucleusId: *********,
      firstName: "Jane",
      lastName: "Doe",
      originEmail: "<EMAIL>",
      dateOfBirth: "1998-12-15",
      country: { label: "United States", name: "United States", value: "US" },
      preferredLanguage: { name: "English", code: "en-us" }
    },
    analytics: { checkedApplicationStatus: () => {} },
    INTERESTED_CREATOR_REAPPLY_PERIOD: true,
    state: {
      exceptionCode: null,
      sessionUser: null,
      isLoading: false
    },
    labels: {
      infoLabels: {
        interestedCreatorTitle: "Interested Creator",
        messages: {
          firstNameTooLong: "firstNameTooLong",
          lastNameTooLong: "lastNameTooLong",
          street: "street",
          streetTooLong: "streetTooLong",
          city: "city",
          cityTooLong: "cityTooLong",
          state: "state",
          stateTooLong: "stateTooLong",
          zipCode: "zipCode",
          zipCodeTooLong: "zipCodeTooLong",
          tShirtSize: "",
          entityType: "",
          businessName: "businessName",
          businessNameTooLong: "businessNameTooLong",
          email: "email",
          emailTooLong: "emailTooLong",
          emailInvalid: "emailInvalid",
          url: "url",
          invalidUrl: "invalidUrl",
          followersMaxLength: "followersMaxLength",
          firstName: "firstName",
          lastName: "lastName",
          dateOfBirth: "dateOfBirth",
          dateOfBirthInvalid: "dateOfBirthInvalid",
          ageMustBe18OrOlder: "Must be 18 or older",
          country: "country"
        }
      },
      translation: {
        messages: {
          preferredEmailTooLong: "preferredEmailTooLong",
          preferredEmailInvalid: "preferredEmailInvalid",
          preferredPhoneNumber: "preferredPhoneNumber",
          preferredPhoneNumberTooLong: "preferredPhoneNumberTooLong",
          contentLanguage: "contentLanguage",
          language: "language",
          preferredEmail: "preferredEmail"
        }
      },
      formLabels: {
        infoTitle: "Your information",
        firstName: "First Name",
        lastName: "Last Name",
        eaEmailID: "EA Email ID",
        creatorEmail: "<EMAIL>",
        dateOfBirth: "Date of Birth",
        preferredEmail: "Preferred Email for Communication",
        country: "Country/Region",
        contentMediaTitle: "Content Media",
        contentMediaDescription: "Please provide links to your content media accounts.",
        contentUrlPlaceholder: "Content URL",
        contentUrl: "Content URL",
        contentFollowers: "Followers",
        contentFollowersPlaceholder: "Followers",
        addAnother: "Add Another",
        contentLanguagesTitle: "Content Languages",
        contentLanguagesDescription: "This is the language(s) in which you produce content in.",
        contentLanguage: "Channel Language(s)",
        languageTitle: "Language for Communication",
        languageDescription: "This is the communication language that EA will use with you.",
        language: "Language",
        cancel: "Cancel",
        next: "Next",
        duplicateUrl: "Duplicate URLs not allowed",
        urlScanFailed: "You cannot submit content from this website/domain at this time.",
        followersMaxLength: "followersMaxLength",
        selectCountry: "Select Country",
        remove: "Remove",
        ok: "OK",
        calendar: "calendar",
        close: "Close",
        connectSocialMediaAccountTitle: "Connect Social Media Accounts",
        connectSocialMediaAccountDescription:
          "Please connect at least one of your social media accounts with your content. You may also  connect multiple accounts on each social media (Not including Instagram). This will help our team assess our compatibility.  For security reasons, please connect your accounts again.",
        additionalContentAndWebsiteTitle: "Additional Content and Website Links",
        additionalContentAndWebsiteDescription:
          "If you want to share specific content and other website links to be reviewed by our team you can add up to 10 links below!",
        websiteUrlLabel: "Website URL(Optional)",
        additionalLinkPlaceholder: "Example: https://www.mywebsite.com/mylink",
        addMoreUrlLabel: "Add another URL",
        invalidUrl: "URL provided is invalid",
        ageMustBe18OrOlder: "Must be 18 or older"
      },
      pageLabels: {
        confirmationDesc1: "confirmationDesc1",
        confirmationDesc2: "confirmationDesc2",
        modalConfirmationTitle: "modalConfirmationTitle",
        interestedCreatorTitle: "Start your submission",
        yes: "yes",
        no: "no",
        interestedUserDescription1: "Hi there",
        interestedUserDescription2: "We need some basic information from you. First off, tell us who you are."
      },
      layout: {
        main: {
          unhandledError: "unhandledError"
        },
        buttons: {
          close: "Close"
        }
      }
    },
    onClose: () => {},
    errorHandling: () => {},
    stableDispatch: dispatch,
    setShowConfirmation: (_a: boolean) => {},
    showConfirmation: false,
    showAddConfirmation: false,
    setShowAddConfirmation: (_a: boolean) => {},
    connectAccountLabels: ConnectAccountLabels,
    accountToRemove: "",
    setAccountToRemove: (_a: string) => {},
    showRemoveAccountModal: false,
    setShowRemoveAccountModal: (_a: boolean) => {},
    pages: [],
    connectAccounts: [
      {
        value: "facebook",
        accountIcon: facebookIcon,
        redirectUrl: "http://localhost:3003/cn-applications-mfe/api/facebook-login"
      },
      {
        value: "instagram",
        accountIcon: instagramIcon,
        redirectUrl: "http://localhost:3003/cn-applications-mfe/api/instagram-login"
      },
      {
        value: "twitch",
        accountIcon: twitchIcon,
        redirectUrl: "http://localhost:3003/cn-applications-mfe/api/twitch-login"
      },
      {
        value: "tiktok",
        accountIcon: tiktokIcon,
        redirectUrl: "http://localhost:3003/cn-applications-mfe/api/tiktok-login"
      },
      {
        value: "youtube",
        accountIcon: youTubeIcon,
        redirectUrl: "http://localhost:3003/cn-applications-mfe/api/youtube-login"
      }
    ],
    locale: "en-us",
    router: {
      locale: "en-us",
      push: () => Promise.resolve(true)
    } as unknown as NextRouter,
    configuration: {
      metadataClient: {
        get: () => Promise.resolve([]),
        patch: () => Promise.resolve()
      } as unknown as TraceableHttpClient,
      applicationsClient: {
        get: () => Promise.resolve([]),
        patch: () => Promise.resolve()
      } as unknown as TraceableHttpClient
    },
    handleCancelRegistration: () => {}
  }
};
