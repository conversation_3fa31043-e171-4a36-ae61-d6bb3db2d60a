import React, { FC, forwardRef, useState } from "react";
import CheckboxCard from "../CheckboxCard/CheckboxCard";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";

interface Item {
  label?: string;
  image?: string;
  checked: boolean;
  icon?: FC<SvgProps>;
  imageAsIcon?: string;
  value?: string;
}

interface CheckboxCardsProps {
  items: Item[];
  value: Item[];
  disabled?: boolean;
  onChange?: (selectedData: Item[]) => void;
  errorMessage?: string;
  readOnly?: boolean;
  selectAlternateItem?: boolean;
  basePath?: string;
}

const CheckboxCards = forwardRef<HTMLDivElement, CheckboxCardsProps>(
  ({ items = [], value, disabled, onChange, errorMessage, readOnly, selectAlternateItem, basePath }, ref) => {
    const [checked, setChecked] = useState<Item[]>(value);

    function checkboxOnChange(data: Item, isChecked: boolean) {
      let selectedData: Item[];
      if (selectAlternateItem) {
        if (!data.checked) {
          selectedData = [data];
        } else {
          selectedData = items.filter((item) => item !== data);
        }
      } else {
        if (isChecked) {
          selectedData = checked.length !== 0 ? [...checked, data] : [data];
        } else {
          selectedData = checked.filter((item) => item.value !== data.value);
        }
      }
      if (onChange) {
        onChange(selectedData);
      }
      setChecked(selectedData);
    }

    return (
      <>
        <div className="ic-ui-card-container" ref={ref}>
          {items.map((item, key) => (
            <CheckboxCard
              key={key}
              disabled={disabled}
              item={item}
              basePath={basePath}
              onChange={checkboxOnChange}
              readOnly={readOnly}
            />
          ))}
        </div>
        {errorMessage && <div className="interested-creators-ui-type-error-message">{errorMessage}</div>}
      </>
    );
  }
);

export default CheckboxCards;
