import React, { FC, memo, MutableRefObject, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dalBody,
  ModalClose<PERSON>utton,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>le,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";

export type ConnectFacebookPagesModalProps = {
  labels: {
    cancel: string;
    connect: string;
    close: string;
    title: string;
  };
  onClose: () => void;
  onConnect: () => void;
  onChange: ({ target }: React.ChangeEvent<HTMLInputElement>) => void;
  pending: boolean;
  selectedPage: {
    pageId: string;
    pageAccessToken: string;
  } | null;
  pages: { accessToken: string; name: string; id: string }[];
};

export type FooterButtonsProps = {
  labels: {
    cancel: string;
    connect: string;
  };
  onClose: () => void;
  onConnect: () => void;
  cancelButtonRef: MutableRefObject<HTMLButtonElement | null>;
  pending: boolean;
  isDisabled: boolean;
};

export type ModalChildrenFbPagesProps = {
  pages: { accessToken: string; name: string; id: string }[];
  onChange: ({ target }: React.ChangeEvent<HTMLInputElement>) => void;
};

export const FooterButtons: FC<FooterButtonsProps> = memo(function FooterButtons({
  cancelButtonRef,
  labels,
  onClose,
  onConnect,
  pending,
  isDisabled
}) {
  return (
    <>
      <Button variant="tertiary" dark size="md" onClick={onClose} disabled={pending} ref={cancelButtonRef}>
        {labels.cancel}
      </Button>
      <Button onClick={onConnect} disabled={isDisabled} spinner={pending}>
        {labels.connect}
      </Button>
    </>
  );
});

export const ModalChildrenFbPages: FC<ModalChildrenFbPagesProps> = memo(function FooterButtons({ pages, onChange }) {
  return (
    <div className="fb-pages-cont">
      {pages.map(({ id, name, accessToken: pageAccessToken }, ind) => {
        return (
          <section className="fb-pages" key={`fb-pag-${ind}`}>
            <input onChange={onChange} name="fb-pages" type="radio" {...{ id, value: pageAccessToken }} />
            <label htmlFor={id}>{name} </label>
          </section>
        );
      })}
    </div>
  );
});

export const ConnectFacebookPagesModal: FC<ConnectFacebookPagesModalProps> = ({
  labels,
  pages,
  onChange,
  onClose,
  onConnect,
  selectedPage,
  pending
}) => {
  const cancelButtonRef = useRef<HTMLButtonElement | null>(null);
  return (
    <ModalV2 closeButtonRef={cancelButtonRef} closeOnOutsideClick={!pending}>
      <ModalHeader>
        <ModalTitle>{labels.title}</ModalTitle>
        <ModalCloseButton
          ariaLabel={labels.close}
          closeButtonRef={cancelButtonRef}
          disabled={pending}
        ></ModalCloseButton>
      </ModalHeader>
      <ModalBody>
        <ModalChildrenFbPages {...{ pages, onChange }} />
      </ModalBody>
      <ModalFooter showDivider>
        <FooterButtons
          {...{ labels, onClose, onConnect, pending, isDisabled: !selectedPage || pending, cancelButtonRef }}
        />
      </ModalFooter>
    </ModalV2>
  );
};

export default ConnectFacebookPagesModal;
