import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import Search from "./Search";

describe("Search Component", () => {
  const searchOptions = [
    { value: "1", label: "Option 1", image: "image1.png" },
    { value: "2", label: "Option 2", image: "image2.png" },
    { value: "3", label: "Option 3", image: "image3.png" }
  ];
  const onChangeMock = jest.fn();

  it("renders with default props", () => {
    render(
      <Search
        value={{ value: "", label: "", image: "" }}
        options={searchOptions}
        onChange={onChangeMock}
        placeholder="Search here..."
      />
    );

    expect(screen.getByPlaceholderText("Search here...")).toBeInTheDocument();
    expect(screen.queryByRole("list")).not.toBeInTheDocument();
  });

  it("opens and closes options list", async () => {
    render(
      <Search
        value={{ value: "", label: "", image: "" }}
        options={searchOptions}
        onChange={onChangeMock}
        placeholder="Search here..."
      />
    );

    const input = await screen.findByPlaceholderText("Search here...");
    fireEvent.click(input);

    expect(await screen.findByRole("list")).toBeInTheDocument();

    fireEvent.blur(input);

    expect(screen.queryByRole("list")).toBeNull();
  });

  test("filters and selects an option", async () => {
    render(
      <Search
        value={{ value: "", label: "", image: "" }}
        options={searchOptions}
        onChange={onChangeMock}
        placeholder="Search here..."
      />
    );

    const input = (await screen.findByPlaceholderText("Search here...")) as HTMLInputElement;
    fireEvent.change(input, { target: { value: "Option 1" } });

    expect(await screen.findByText("Option 1")).toBeInTheDocument();
    fireEvent.mouseDown(await screen.findByText("Option 1"));

    expect(input.value).toBe("Option 1");
    expect(onChangeMock).toHaveBeenCalledWith({ value: "1", label: "Option 1", image: "image1.png" });
  });
});
