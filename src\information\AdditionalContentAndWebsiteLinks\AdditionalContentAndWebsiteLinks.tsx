import { Controller, FieldErrors, useFieldArray, useFormContext } from "react-hook-form";
import React, { memo, useCallback, useEffect } from "react";
import { Icon, Input, outlineAdd, remove as removeIcon } from "@eait-playerexp-cn/core-ui-kit";
import cx from "classnames";
import { Rules } from "../Information";

export type FormLabels = {
  additionalContentAndWebsiteTitle: string;
  additionalContentAndWebsiteDescription: string;
  additionalLinkPlaceholder: string;
  websiteUrlLabel: string;
  addMoreUrlLabel: string;
  remove: string;
  duplicateUrl: string;
};

export type additionalContentAndWebsiteDescriptionProps = {
  labels: FormLabels;
  rules: Rules;
};

export type FormData = {
  contentUrls: {
    url: string;
    followers: string;
    saved: boolean;
  }[];
};

const additionalContentAndWebsiteDescription = memo(function additionalContentAndWebsiteDescription({
  labels: formLabels,
  rules
}: additionalContentAndWebsiteDescriptionProps) {
  const methods = useFormContext<FormData>();
  const { control, setError, trigger, watch, formState, clearErrors } = methods;
  const { fields, append, update, remove } = useFieldArray({
    control,
    name: "contentUrls"
  });
  const contentUrlChanged = watch("contentUrls");
  const clearInput = useCallback(
    (index: number) => () => {
      const defaultValue = "https://";
      update(index, { ...contentUrlChanged[index], url: defaultValue, saved: false });
      trigger(`contentUrls.${index}.url`);
    },
    [update, trigger, JSON.stringify(contentUrlChanged)]
  );
  const onDelete = useCallback((index: number) => () => remove(index), [remove]);
  const onAddUrls = useCallback(() => {
    append({ url: "", saved: false, followers: "" });
    trigger(`contentUrls`);
  }, [append, trigger]);

  useEffect(() => {
    let timer: ReturnType<typeof setTimeout>;
    const urlFields = contentUrlChanged?.map(({ url }: { url: string }) => url.trim());
    // this will trigger error on duplicate index
    urlFields?.forEach((item: string, index: number) => {
      const duplicateFound = urlFields.indexOf(item) !== index;
      if (duplicateFound && item.trim() && item.trim() !== "https://") {
        const contentUrlErrors: FieldErrors<FormData>["contentUrls"] = formState.errors.contentUrls;
        if (contentUrlErrors?.[index]?.url?.message !== formLabels.duplicateUrl) {
          timer = setTimeout(() => {
            setError(`contentUrls.${index}.url`, { type: "manual", message: formLabels.duplicateUrl });
          });
        }
      } else {
        if (formState?.errors?.contentUrls?.[index]?.url?.message === formLabels.duplicateUrl) {
          clearErrors(`contentUrls.${index}.url`);
          trigger("contentUrls");
        }
      }
    }, []);
    return () => clearTimeout(timer);
  }, [JSON.stringify(formState.errors), formLabels.duplicateUrl, JSON.stringify(contentUrlChanged)]);

  return (
    <div className="interested-creators-ui-additional-content">
      <h4 className="interested-creators-ui-additional-content-title">{formLabels.additionalContentAndWebsiteTitle}</h4>
      <p className="interested-creators-ui-additional-content-description">
        {formLabels.additionalContentAndWebsiteDescription}
      </p>
      <ul
        className="interested-creators-ui-additional-content-urls"
        aria-label="interested-creators-ui-additional-content-urls"
      >
        {fields.map((item, index) => {
          return (
            <li
              key={item.id}
              className={cx("interested-creators-ui-additional-content-url", {
                "interested-creators-ui-additional-content-url-without-delete": index === 0
              })}
            >
              <Controller
                control={control}
                name={`contentUrls.${index}.url`}
                key={`contentUrls.${index}.url`}
                rules={rules?.url}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    dark
                    errorMessage={error?.message || ""}
                    id={`contentUrls.${index}.url`}
                    {...field}
                    placeholder={formLabels.additionalLinkPlaceholder}
                    label={formLabels.websiteUrlLabel}
                    clearContent
                    onClearContent={clearInput(index)}
                    type="url"
                  />
                )}
              />
              {!!index && (
                <button
                  type="button"
                  aria-label={formLabels.remove}
                  className="interested-creators-ui-additional-content-delete"
                  onClick={onDelete(index)}
                >
                  <Icon
                    icon={removeIcon}
                    className="interested-creators-ui-content-url-icon"
                    id="interested-creators-ui-content-url-icon"
                  />
                </button>
              )}
            </li>
          );
        })}
      </ul>
      {fields.length < 10 && (
        <button type="button" className="interested-creators-ui-additional-content-add-more" onClick={onAddUrls}>
          <Icon
            icon={outlineAdd}
            className="interested-creators-ui-content-url-add-more-icon"
            id="interested-creators-ui-content-url-add-more-icon"
          />
          <p className="interested-creators-ui-content-url-add-more-text">{formLabels.addMoreUrlLabel}</p>
        </button>
      )}
    </div>
  );
});

export default additionalContentAndWebsiteDescription;
