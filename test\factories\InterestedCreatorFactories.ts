import { Factory } from "fishery";
import Random from "./Random";
import { aPrimaryFranchise, aSecondaryFranchise } from "./FranchiseFactories";
import { aCreatorType, aLanguage } from "@eait-playerexp-cn/metadata-test-fixtures";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { InterestedCreator } from "../../src/Browser/InterestedCreatorsService";

const factory = Factory.define(() => ({
  nucleusId: Random.nucleusId(),
  firstName: Random.firstName(),
  lastName: Random.lastName(),
  originEmail: Random.email(),
  dateOfBirth: LocalizedDate.epochMinusMonths(240),
  preferredEmail: Random.email(),
  countryCode: Random.countryCode(),
  creatorTypes: [aCreatorType(), aCreatorType()],
  contentLanguages: [aLanguage(), aLanguage()],
  contentAccounts: [],
  preferredFranchises: [aPrimaryFranchise(), aSecondaryFranchise()],
  additionalLinks: []
}));

export function anInterestedCreator(override = {}): InterestedCreator {
  return factory.build(override) as InterestedCreator;
}
