import React, { FC, useEffect } from "react";
import Link from "next/link";
import Pending from "../styles/img/icons/Pending";
import { BrowserAnalytics } from "../utils/types";
import RequestsToJoinStatusTable from "../RequestsToJoinStatusTable/RequestsToJoinStatusTable";

export type Labels = {
  title: string;
  subTitle: string;
  applicationPendingDescription: string;
  description: string;
  returnToHomePage: string;
  pending: string;
  email: string;
  programName: string;
  programLabel: string;
  status: string;
  unReviewed: string;
  submissionReceived: string;
  submissionReceivedDescription: string;
  submissionDate: string;
  submissionUpdate: string;
  submissionUpdateDescription: string;
  reviewAndResubmit: string;
  creatorNetwork: string;
  close: string;
  applicationPendingThumbnailAltText: string;
};
export type ApplicationPendingPageProps = {
  labels: Labels;
  emailId: string;
  locale: string;
  analytics: BrowserAnalytics;
  INTERESTED_CREATOR_REAPPLY_PERIOD?: boolean;
  canApply?: boolean;
  submittedDate?: string;
  redirectedToInformationForm: string;
  redirectedToMain: string;
  applicationPendingThumbnail: string;
};

const ApplicationPendingPage: FC<ApplicationPendingPageProps> = ({
  emailId,
  locale,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  canApply,
  submittedDate,
  redirectedToInformationForm,
  redirectedToMain,
  labels,
  applicationPendingThumbnail
}) => {
  const {
    title,
    subTitle,
    description,
    returnToHomePage,
    pending,
    unReviewed,
    submissionReceived,
    applicationPendingDescription,
    submissionUpdate,
    submissionUpdateDescription,
    reviewAndResubmit,
    applicationPendingThumbnailAltText
  } = labels;

  useEffect(() => {
    if (analytics.checkedApplicationStatus) {
      analytics.checkedApplicationStatus({ locale, status: "Pending" });
    }
  }, [locale]);

  return (
    <div className="mg-ic-ui-page-interested-creator">
      <div className="application-pending-wrapper">
        {INTERESTED_CREATOR_REAPPLY_PERIOD && canApply && (
          <img
            className="application-pending-thumbnail"
            src={applicationPendingThumbnail}
            alt={applicationPendingThumbnailAltText}
          />
        )}
        <div className="application-pending-content-container">
          <h3 className="application-pending-title">
            {INTERESTED_CREATOR_REAPPLY_PERIOD ? (canApply ? submissionUpdate : title) : submissionReceived}
          </h3>
          <h4 className="application-pending-sub-title">{subTitle}</h4>
          <div className="application-pending-description">
            {INTERESTED_CREATOR_REAPPLY_PERIOD
              ? canApply
                ? submissionUpdateDescription
                : applicationPendingDescription
              : description}
          </div>
        </div>
        <div>
          <RequestsToJoinStatusTable
            requestsToJoinStatusTableLabels={labels}
            emailId={emailId}
            INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
            submittedDate={submittedDate}
            statusIcon={Pending}
            applicationStatus={INTERESTED_CREATOR_REAPPLY_PERIOD && canApply ? unReviewed : pending}
          />
        </div>

        {!canApply && (
          <div className="application-pending-back-home">
            <Link href="/api/logout" className="btn btn-primary btn-md">
              {returnToHomePage}
            </Link>
          </div>
        )}
        {INTERESTED_CREATOR_REAPPLY_PERIOD && canApply && (
          <div className="application-pending-footer">
            <Link href={redirectedToMain} className="btn btn-secondary btn-md">
              {returnToHomePage}
            </Link>

            <Link href={redirectedToInformationForm} className="btn btn-primary btn-md">
              {reviewAndResubmit}
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApplicationPendingPage;
