export const communicationPreferencesLabels = {
  title: "Communication Preferences",
  profileTitle: "Communication",
  description: "Let us know your communication preferences and languages.",
  modalConfirmationTitle: "Are you sure you want to cancel registration?",
  confirmationDesc1: "Pressing Yes will quit the registration process.",
  confirmationDesc2: "You can begin registration again at any point by visiting the Creator Network Website.",
  success: {
    updatedInformationHeader: "Information update successful",
    preferredEmail: "You have successfully updated your Preferred Email.",
    preferredPhoneNumber: "You have successfully updated your Preferred Phone Number.",
    preferredLanguage: "You have successfully updated your Preferred Language.",
    contentLanguage: "You have successfully updated your Preferred Content Languages."
  },
  messages: {
    preferredEmail: "Preferred Email Address is required",
    preferredEmailTooLong: "Preferred Email Address is too long",
    preferredEmailInvalid: "Preferred Email Address is invalid",
    preferredPhoneNumber: "Phone Number is required",
    preferredPhoneNumberTooLong: "Phone Number is too long",
    contentLanguage: "Content Language is required",
    language: "Language is required"
  },
  labels: {
    addDiscord: "Add Discord",
    discordTitle: "Discord Communication",
    discordDescription:
      "Connect your Discord channel to chat with your Community Manager and to get updates about any opportunities you join.",
    preferredEmailAddressTitle: "Preferred Email Address for Communication",
    preferredEmailAddressDescription:
      "All communication from Creator Network such as opportunities, legal, travel, payments and events will be sent to your preferred email address.",
    preferredEmail: "Email Address",
    preferredPhoneNumberTitle: "Preferred Phone Number",
    preferredPhoneNumber: "Phone Number",
    contentLanguagesTitle: "Content Languages",
    contentLanguagesDescription: "This is the language(s) in which you produce content in.",
    contentLanguage: "Channel Language(s)",
    languageTitle: "Language for Communication",
    languageDescription: "This is the communication language that Electronic Arts will use with you.",
    language: "Language"
  }
};
