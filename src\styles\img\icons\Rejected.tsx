import React, { FC } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";

const Icon: FC<SvgProps> = (props) => {
  return (
    <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M5.8125 0C6.84375 0 7.82812 0.28125 8.71875 0.796875C9.60938 1.3125 10.3125 2.01562 10.8281 2.90625C11.3438 3.79688 11.625 4.78125 11.625 5.8125C11.625 6.86719 11.3438 7.82812 10.8281 8.71875C10.3125 9.60938 9.60938 10.3359 8.71875 10.8516C7.82812 11.3672 6.84375 11.625 5.8125 11.625C4.75781 11.625 3.79688 11.3672 2.90625 10.8516C2.01562 10.3359 1.28906 9.60938 0.773438 8.71875C0.257812 7.82812 0 6.86719 0 5.8125C0 4.78125 0.257812 3.79688 0.773438 2.90625C1.28906 2.01562 2.01562 1.3125 2.90625 0.796875C3.79688 0.28125 4.75781 0 5.8125 0ZM8.67188 7.33594L7.125 5.8125L8.67188 4.28906C8.71875 4.24219 8.74219 4.17188 8.74219 4.10156C8.74219 4.03125 8.71875 3.96094 8.67188 3.89062L7.73438 2.95312C7.6875 2.90625 7.61719 2.88281 7.54688 2.88281C7.45312 2.88281 7.38281 2.90625 7.33594 2.95312L5.8125 4.5L4.28906 2.95312C4.21875 2.90625 4.14844 2.88281 4.07812 2.88281C4.00781 2.88281 3.9375 2.90625 3.89062 2.95312L2.95312 3.89062C2.90625 3.9375 2.88281 4.00781 2.88281 4.07812C2.88281 4.17188 2.90625 4.24219 2.95312 4.28906L4.5 5.8125L2.95312 7.33594C2.90625 7.40625 2.88281 7.47656 2.88281 7.54688C2.88281 7.61719 2.90625 7.6875 2.95312 7.73438L3.89062 8.67188C3.9375 8.71875 3.98438 8.74219 4.07812 8.74219C4.14844 8.74219 4.21875 8.71875 4.28906 8.67188L5.8125 7.125L7.33594 8.67188C7.38281 8.71875 7.45312 8.74219 7.52344 8.74219C7.59375 8.74219 7.66406 8.71875 7.73438 8.67188L8.67188 7.73438C8.71875 7.6875 8.74219 7.64062 8.74219 7.54688C8.74219 7.47656 8.71875 7.40625 8.67188 7.33594Z"
        fill="#FF1414"
      />
    </svg>
  );
};

Icon.defaultProps = {
  className: "icon",
  color: "currentColor"
};

export default Icon;
