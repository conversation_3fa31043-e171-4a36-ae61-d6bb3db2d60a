import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { NextRouter } from "next/router";
import CreatorType from "./CreatorType";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

const meta: Meta<typeof CreatorType> = {
  title: "Component Library/Creator Type Page",
  component: CreatorType,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof CreatorType>;

const dispatch = (): void => {};

export const CreatorTypePage: Story = {
  args: {
    redirectedToNextStepUrl: "/information/franchises-you-play",
    interestedCreator: {
      defaultGamerTag: "Gamer123",
      nucleusId: 987654321,
      firstName: "<PERSON>",
      lastName: "Doe",
      originEmail: "<EMAIL>",
      dateOfBirth: "1990-01-01",
      country: { label: "United States", name: "United States", value: "US" },
      creatorTypes: []
    },
    analytics: { checkedApplicationStatus: () => {} },
    INTERESTED_CREATOR_REAPPLY_PERIOD: true,
    state: {
      exceptionCode: null,
      sessionUser: null,
      isLoading: false
    },
    labels: {
      cancel: "Cancel",
      next: "Next",
      yes: "Yes",
      no: "No",
      close: "Close",
      title: "Are you sure you want to Cancel?",
      description:
        "if you change your mind, you can always come back and accept the invite later cancel and join back when you like.",
      interestedCreatorTitle: "Choose your Creator Type",
      interestedCreatorDescription: "Select the types of creator that fits the content you create. ",
      confirmationDesc1: "Confirmation Description 1",
      confirmationDesc2: "Confirmation Description 2",
      modalConfirmationTitle: "Modal Confirmation Title",
      unhandledError: "Unhandled Error",
      requiredMessage: "This field is required",
      creatorsTypeLabels: [
        {
          value: "YOUTUBER",
          label: "YouTuber"
        },
        {
          value: "LIFESTYLE",
          label: "Lifestyle"
        },
        {
          value: "PHOTOGRAPHER",
          label: "Photographer"
        },
        {
          value: "DESIGNER_ARTIST",
          label: "Designer/Artist"
        },
        {
          value: "BLOGGER",
          label: "Blogger"
        },
        {
          value: "LIVE_STREAMER",
          label: "Live Streamer"
        },
        {
          value: "PODCASTER",
          label: "Podcaster"
        },
        {
          value: "COSPLAYER",
          label: "Cosplayer"
        },
        {
          value: "ANIMATOR",
          label: "Animator"
        },
        {
          value: "SCREENSHOTER",
          label: "Screenshoter"
        },
        {
          value: "OTHER",
          label: "Other"
        }
      ]
    },
    errorToast: () => {},
    onClose: () => {},
    errorHandling: () => {},
    stableDispatch: dispatch,
    setShowConfirmation: (_a: boolean) => {},
    showConfirmation: false,
    router: {
      locale: "en-us",
      push: () => Promise.resolve(true)
    } as unknown as NextRouter,
    configuration: {
      applicationsClient: {
        get: () => Promise.resolve([]),
        patch: () => Promise.resolve()
      } as unknown as TraceableHttpClient,
      metadataClient: {
        get: () =>
          Promise.resolve({
            data: [
              {
                value: "YOUTUBER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/YOUTUBER.png",
                label: "youtuber"
              },
              {
                value: "LIFESTYLE",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/LIFESTYLE.png",
                label: "lifestyle"
              },
              {
                value: "PHOTOGRAPHER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/PHOTOGRAPHER.png",
                label: "photographer"
              },
              {
                value: "DESIGNER_ARTIST",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/DESIGNER_ARTIST.png",
                label: "designer_artist"
              },
              {
                value: "BLOGGER",
                imageAsIcon: "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/BLOGGER.png",
                label: "blogger"
              },
              {
                value: "LIVE_STREAMER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/LIVE_STREAMER.png",
                label: "live_streamer"
              },
              {
                value: "PODCASTER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/PODCASTER.png",
                label: "podcaster"
              },
              {
                value: "COSPLAYER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/COSPLAYER.png",
                label: "cosplayer"
              },
              {
                value: "ANIMATOR",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/ANIMATOR.png",
                label: "animator"
              },
              {
                value: "SCREENSHOTER",
                imageAsIcon:
                  "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/SCREENSHOTER.png",
                label: "screenshoter"
              },
              {
                value: "OTHER",
                imageAsIcon: "https://eait-playerexp-cn-website-metadata.s3.amazonaws.com/img/creatortypes/OTHER.png",
                label: "other"
              }
            ]
          }),
        patch: () => Promise.resolve()
      } as unknown as TraceableHttpClient
    },
    handleCancelRegistration: () => {},
    basePath: "/support-a-creator"
  }
};
