import React, { FC } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";

const Icon: FC<SvgProps> = (props) => {
  return (
    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M5.81247 0.981299C6.84371 0.977981 7.82899 1.25606 8.72127 1.76882C9.61355 2.28158 10.3189 2.98244 10.8374 3.8714C11.3559 4.76036 11.6403 5.74382 11.6436 6.77507C11.647 7.82975 11.3689 8.79159 10.8561 9.68387C10.3434 10.5761 9.64258 11.305 8.75362 11.8235C7.86466 12.3419 6.88112 12.6029 5.84987 12.6062C4.79519 12.6096 3.83343 12.3549 2.94115 11.8422C2.04887 11.3294 1.31997 10.6052 0.801485 9.71622C0.282997 8.82725 0.0220943 7.86715 0.0187009 6.81247C0.015383 5.78123 0.270027 4.79603 0.782784 3.90375C1.29554 3.01147 2.01984 2.30601 2.9088 1.78752C3.79776 1.26903 4.75779 0.984692 5.81247 0.981299ZM7.17479 9.18008C7.22182 9.22681 7.29221 9.25002 7.38588 9.22628C7.45619 9.22605 7.52635 9.17895 7.573 9.10849L8.22638 8.21576C8.27311 8.16873 8.27288 8.09842 8.27258 8.00467C8.27235 7.93436 8.22525 7.8642 8.17823 7.81747L6.67477 6.74418L6.66436 3.50982C6.66413 3.43951 6.61703 3.36935 6.57001 3.32262C6.52298 3.2759 6.45252 3.22925 6.38221 3.22948L5.25721 3.2331C5.16346 3.2334 5.0933 3.2805 5.04658 3.32753C4.99985 3.37455 4.97664 3.44494 4.97687 3.51525L4.98954 7.45273C4.98984 7.54648 5.01358 7.64015 5.10748 7.68673L7.17479 9.18008Z"
        fill="#FFC34D"
      />
    </svg>
  );
};

Icon.defaultProps = {
  className: "icon",
  color: "currentColor"
};

export default Icon;
