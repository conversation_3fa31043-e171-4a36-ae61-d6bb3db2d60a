import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import Search from "./Search";
import React, { ComponentType, ReactElement } from "react";

const meta: Meta<typeof Search> = {
  title: "Component Library/Search",
  component: Search,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof Search>;

export const SearchWithPlaceholder: Story = {
  args: {
    value: { value: "", label: "", image: "" },
    options: [
      { value: "1", label: "Option 1", image: "image1.png" },
      { value: "2", label: "Option 2", image: "image2.png" },
      { value: "3", label: "Option 3", image: "image3.png" }
    ],
    onChange: () => {},
    placeholder: "Search here..."
  }
};

export const SearchWithDeaultValue: Story = {
  args: {
    value: { value: "1", label: "Option 1", image: "image1.png" },
    options: [
      { value: "1", label: "Option 1", image: "image1.png" },
      { value: "2", label: "Option 2", image: "image2.png" },
      { value: "3", label: "Option 3", image: "image3.png" }
    ],
    onChange: () => {},
    placeholder: "Search here..."
  }
};

export const SearchWithError: Story = {
  args: {
    value: { value: "", label: "", image: "" },
    options: [],
    onChange: () => {},
    placeholder: "Search here...",
    errorMessage: "An error occured"
  }
};
