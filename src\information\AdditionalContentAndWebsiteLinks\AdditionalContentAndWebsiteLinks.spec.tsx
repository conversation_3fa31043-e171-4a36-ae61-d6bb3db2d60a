import React from "react";
import { render, screen } from "@testing-library/react";
import { FormProvider, useForm } from "react-hook-form";
import AdditionalContentAndWebsiteLinks from "./AdditionalContentAndWebsiteLinks";
import CreatorForm from "../../utils/CreatorForm";
import { informationLabels } from "../../../test/translations/Information";
import { interestedCreatorInformationFormLabels } from "../../../test/translations/InterestedCreatorInformationForm";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import Random from "../../../test/factories/Random";

describe("AdditionalContentAndWebsiteLinks", () => {
  const allRules = CreatorForm.rules(informationLabels);
  const { formLabels } = interestedCreatorInformationFormLabels;
  const { url } = allRules;
  const rules = { url };
  // Wrapper component to provide form context
  const FormWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const methods = useForm({ defaultValues: { contentUrls: [{ url: "", followers: "" }] } });
    return <FormProvider {...methods}>{children}</FormProvider>;
  };

  it("shows the content with default props", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks labels={formLabels} rules={rules} />
      </FormWrapper>
    );
    const contentTitle = screen.getByRole("heading", {
      level: 4,
      name: formLabels.additionalContentAndWebsiteTitle
    });

    expect(contentTitle).toBeInTheDocument();
    expect(contentTitle).toHaveClass("interested-creators-ui-additional-content-title");
    expect(screen.getByText(formLabels.additionalContentAndWebsiteDescription)).toBeInTheDocument();
    expect(screen.getByText(formLabels.additionalContentAndWebsiteDescription)).toHaveClass(
      "interested-creators-ui-additional-content-description"
    );

    expect(screen.getByRole("textbox", { name: formLabels.websiteUrlLabel })).toBeInTheDocument();
    expect(screen.getByRole("textbox", { name: formLabels.websiteUrlLabel })).toHaveAttribute(
      "placeholder",
      formLabels.additionalLinkPlaceholder
    );
    expect(screen.getByRole("list", { name: "interested-creators-ui-additional-content-urls" })).toBeInTheDocument();
    expect(screen.getByRole("list", { name: "interested-creators-ui-additional-content-urls" })).toHaveClass(
      "interested-creators-ui-additional-content-urls"
    );
    expect(screen.getByRole("button", { name: formLabels.addMoreUrlLabel })).toBeInTheDocument();
    expect(screen.getByTestId("interested-creators-ui-content-url-add-more-icon")).toBeInTheDocument();
    expect(screen.getByTestId("interested-creators-ui-content-url-add-more-icon")).toHaveClass(
      "interested-creators-ui-content-url-add-more-icon"
    );
    expect(screen.getByText(formLabels.addMoreUrlLabel)).toBeInTheDocument();
    expect(screen.getByText(formLabels.addMoreUrlLabel)).toHaveClass(
      "interested-creators-ui-content-url-add-more-text"
    );
  });

  it("shows second input box with delete icon when Add button is clicked", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks labels={formLabels} rules={rules} />
      </FormWrapper>
    );

    await userEvent.click(screen.getByRole("button", { name: formLabels.addMoreUrlLabel }));

    expect(screen.getAllByRole("textbox", { name: formLabels.websiteUrlLabel }).length).toBe(2);
    expect(screen.getByTestId("interested-creators-ui-content-url-icon")).toBeInTheDocument();
  });

  it("removes input box when delete icon is clicked", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks labels={formLabels} rules={rules} />
      </FormWrapper>
    );
    await userEvent.click(screen.getByRole("button", { name: formLabels.addMoreUrlLabel }));
    expect(screen.getAllByRole("textbox", { name: formLabels.websiteUrlLabel }).length).toBe(2);
    expect(screen.getByTestId("interested-creators-ui-content-url-icon")).toBeInTheDocument();

    await userEvent.click(screen.getByRole("button", { name: formLabels.remove }));

    expect(screen.getAllByRole("textbox", { name: formLabels.websiteUrlLabel }).length).toBe(1);
    expect(screen.queryByTestId("interested-creators-ui-content-url-icon")).not.toBeInTheDocument();
  });

  it("clears input when clear icon is clicked", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks labels={formLabels} rules={rules} />
      </FormWrapper>
    );
    const url = Random.url();
    await userEvent.type(screen.getAllByRole("textbox", { name: formLabels.websiteUrlLabel })[0], url);
    expect(screen.getByLabelText(formLabels.websiteUrlLabel)).toHaveValue(url);

    await userEvent.click(screen.getByRole("button", { name: `Clear ${formLabels.websiteUrlLabel}` }));

    expect(screen.getByLabelText(formLabels.websiteUrlLabel)).toHaveValue("https://");
  });

  it("shows error label when duplicate url is entered", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks labels={formLabels} rules={rules} />
      </FormWrapper>
    );
    const url = Random.url();
    await userEvent.type(screen.getAllByRole("textbox", { name: formLabels.websiteUrlLabel })[0], url);
    await userEvent.click(screen.getByRole("button", { name: formLabels.addMoreUrlLabel }));

    await userEvent.type(screen.getAllByRole("textbox", { name: formLabels.websiteUrlLabel })[1], url);

    expect(screen.getByText(formLabels.duplicateUrl)).toBeInTheDocument();
  });

  it("clears the error when duplicate url is modified", async () => {
    render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks labels={formLabels} rules={rules} />
      </FormWrapper>
    );
    const url1 = Random.url();
    const url2 = Random.url();
    await userEvent.type(screen.getAllByText(formLabels.websiteUrlLabel)[0], url1);
    await userEvent.click(screen.getByRole("button", { name: formLabels.addMoreUrlLabel }));
    expect(screen.getAllByText(formLabels.websiteUrlLabel).length).toBe(2);
    await userEvent.type(screen.getAllByText(formLabels.websiteUrlLabel)[1], url1);
    expect(screen.getByText(formLabels.duplicateUrl)).toBeInTheDocument();

    await userEvent.type(screen.getAllByText(formLabels.websiteUrlLabel)[1], url2);

    expect(screen.queryByText(formLabels.duplicateUrl)).not.toBeInTheDocument();
  });

  it("is accessible", async () => {
    const { container } = render(
      <FormWrapper>
        <AdditionalContentAndWebsiteLinks labels={formLabels} rules={rules} />
      </FormWrapper>
    );

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
