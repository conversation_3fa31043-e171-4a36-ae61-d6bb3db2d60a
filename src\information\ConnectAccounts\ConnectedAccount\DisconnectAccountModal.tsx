import React, { FC, memo, MutableRefObject, useRef } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>lose<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>le,
  ModalV2
} from "@eait-playerexp-cn/core-ui-kit";

export type DisconnectAccountModalProps = {
  labels: {
    cancel: string;
    remove: string;
    close: string;
    title: string;
    removeAccountDescription1: string;
    removeAccountDescription2: string;
  };
  onCancel: () => void;
  onRemove: () => void;
  isPending: boolean;
};

export type FooterButtonsProps = {
  labels: {
    cancel: string;
    remove: string;
  };
  onCancel: () => void;
  onRemove: () => void;
  cancelButtonRef: MutableRefObject<HTMLButtonElement | null>;
  isPending: boolean;
};

export const FooterButtons: FC<FooterButtonsProps> = memo(function FooterButtons({
  onCancel,
  onRemove,
  cancelButtonRef,
  labels,
  isPending
}) {
  return (
    <>
      <Button variant="tertiary" dark size="md" onClick={onCancel} ref={cancelButtonRef}>
        {labels.cancel}
      </Button>
      <Button onClick={onRemove} spinner={isPending} disabled={isPending}>
        {labels.remove}
      </Button>
    </>
  );
});

export const DisconnectAccountModal: FC<DisconnectAccountModalProps> = ({ labels, onCancel, onRemove, isPending }) => {
  const cancelButtonRef = useRef<HTMLButtonElement | null>(null);
  return (
    <ModalV2 closeButtonRef={cancelButtonRef}>
      <ModalHeader>
        <ModalTitle>{labels.title}</ModalTitle>
        <ModalCloseButton ariaLabel={labels.close} closeButtonRef={cancelButtonRef}></ModalCloseButton>
      </ModalHeader>
      <ModalBody>
        <p>{labels.removeAccountDescription1}</p>
        <p className="disconnect-account-modal-confirmation-additional-desc">{labels.removeAccountDescription2}</p>
      </ModalBody>
      <ModalFooter showDivider>
        <FooterButtons
          {...{
            labels,
            onCancel,
            onRemove,
            isPending,
            cancelButtonRef
          }}
        />
      </ModalFooter>
    </ModalV2>
  );
};

export default DisconnectAccountModal;
