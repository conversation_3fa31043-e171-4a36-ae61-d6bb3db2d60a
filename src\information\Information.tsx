import React, { FC, ReactElement } from "react";
import { NextRouter } from "next/router";
import { BrowserAnalytics, CloseHandler, Dispatch, ErrorHandling, PreferredLanguage, State } from "../utils";
import CreatorForm, { CommunicationFormRules, CreatorFormRules } from "../utils/CreatorForm";
import InterestedCreatorsInformationPage, {
  ConnectAccountLabels,
  Layout,
  PageLabels
} from "./InterestedCreatorsInformationPage/InterestedCreatorsInformationPage";
import { FormLabels } from "./InterestedCreatorInformationForm/InterestedCreatorInformationForm";
import { Country, Language } from "@eait-playerexp-cn/metadata-types";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";

export type ContentUrl = {
  url: string;
  followers: string;
};
type Fbpage = {
  accessToken: string;
  id: string;
  name: string;
};
export type Fbpages = {
  pages: Array<Fbpage>;
};
export type ContentUrlWithoutFollowers = {
  url: string;
};

export type ConnectAccounts = Array<{ value: string; accountIcon: FC<SvgProps>; redirectUrl: string }>;

export type Information = {
  nucleusId: number;
  firstName?: string;
  lastName?: string;
  defaultGamerTag: string;
  originEmail: string;
  contentLanguages?: Array<Language>;
  preferredLanguage?: PreferredLanguage;
  contentUrls?: Array<ContentUrl>;
  country?: Country;
  dateOfBirth?: string;
  countryCode?: string;
  contentAccounts?: Array<ContentUrl>;
};

export type Labels = {
  infoLabels: {
    interestedCreatorTitle: string;
    messages: {
      firstNameTooLong: string;
      lastNameTooLong: string;
      street: string;
      streetTooLong: string;
      city: string;
      cityTooLong: string;
      state: string;
      stateTooLong: string;
      zipCode: string;
      zipCodeTooLong: string;
      tShirtSize: string;
      entityType: string;
      businessName: string;
      businessNameTooLong: string;
      email: string;
      emailTooLong: string;
      emailInvalid: string;
      url: string;
      invalidUrl: string;
      followersMaxLength: string;
      firstName: string;
      lastName: string;
      dateOfBirth: string;
      dateOfBirthInvalid: string;
      ageMustBe18OrOlder: string;
      country: string;
    };
  };
  translation: {
    messages: {
      preferredEmailTooLong: string;
      preferredEmailInvalid: string;
      preferredPhoneNumber: string;
      preferredPhoneNumberTooLong: string;
      contentLanguage: string;
      language: string;
      preferredEmail: string;
    };
  };
  formLabels: FormLabels;
  pageLabels: PageLabels;
  layout: Layout;
};
export type Configuration = {
  metadataClient: TraceableHttpClient;
  applicationsClient: TraceableHttpClient;
  supportedFranchises?: string[];
};
export type InterestedCreatorInformationProps = {
  interestedCreator: Information;
  analytics: BrowserAnalytics;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  stableDispatch: Dispatch;
  state: State;
  labels: Labels;
  redirectedToNextStepUrl: string;
  errorHandling: ErrorHandling;
  configuration: Configuration;
  onClose: () => void;
  showConfirmation: boolean;
  setShowConfirmation: (value: boolean) => void;
  router: NextRouter;
  locale: string;
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  connectAccountLabels: ConnectAccountLabels;
  accountToRemove: string;
  setAccountToRemove: (accountToRemove: string) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  connectAccounts: ConnectAccounts;
  handleCancelRegistration: () => void;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  warning: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
} & Fbpages;

export type Rules = Partial<CreatorFormRules & CommunicationFormRules>;

export const interestedCreatorPages = {
  information: "Information",
  creatorTypes: "CreatorTypes",
  franchises: "FranchisesYouPlay"
};
const InterestedCreatorInformation: FC<InterestedCreatorInformationProps> = ({
  interestedCreator,
  analytics,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  stableDispatch,
  state,
  labels,
  redirectedToNextStepUrl,
  errorHandling,
  configuration,
  onClose,
  showConfirmation,
  setShowConfirmation,
  router,
  locale,
  showAddConfirmation,
  setShowAddConfirmation,
  connectAccountLabels,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  pages,
  connectAccounts,
  errorToast,
  warning,
  handleCancelRegistration
}) => {
  const { infoLabels, translation } = labels;
  const allRules = {
    ...CreatorForm.rules(infoLabels),
    ...CreatorForm.communicationRules(translation)
  };
  const {
    firstName,
    lastName,
    dateOfBirth,
    interestedCreatorCountry,
    preferredEmail,
    contentLanguage,
    url,
    followers
  } = allRules;
  const rules: Rules = {
    firstName,
    lastName,
    dateOfBirth,
    interestedCreatorCountry,
    preferredEmail,
    contentLanguage,
    url,
    followers
  };

  const { formLabels, pageLabels, layout } = labels;
  return (
    <div className="interested-creators-ui">
      <InterestedCreatorsInformationPage
        formLabels={formLabels}
        pageLabels={pageLabels}
        onClose={onClose}
        showConfirmation={showConfirmation}
        interestedCreator={interestedCreator}
        rules={rules}
        stableDispatch={stableDispatch}
        setShowConfirmation={setShowConfirmation}
        router={router}
        locale={locale}
        handleCancelRegistration={handleCancelRegistration}
        analytics={analytics}
        INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
        state={state}
        errorHandling={errorHandling}
        layout={layout}
        redirectedToNextStepUrl={redirectedToNextStepUrl}
        configuration={configuration}
        showAddConfirmation={showAddConfirmation}
        setShowAddConfirmation={setShowAddConfirmation}
        connectAccountLabels={connectAccountLabels}
        accountToRemove={accountToRemove}
        setAccountToRemove={setAccountToRemove}
        showRemoveAccountModal={showRemoveAccountModal}
        setShowRemoveAccountModal={setShowRemoveAccountModal}
        pages={pages}
        connectAccounts={connectAccounts}
        errorToast={errorToast}
        warning={warning}
      />
    </div>
  );
};

export default InterestedCreatorInformation;
