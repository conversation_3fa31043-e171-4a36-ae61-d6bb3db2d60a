import React from "react";
import { screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { renderPage } from "../../../../test/helpers/Page";
import ConnectFacebookPagesModal from "./ConnectFacebookPagesModal";
import { ConnectAccountLabels } from "../../../../test/translations/ConnectAccounts";

describe("ConnectFacebookPagesModal", () => {
  const connectFacebookPagesModalProps = {
    labels: {
      title: ConnectAccountLabels.modalConfirmationTitleFB,
      connect: ConnectAccountLabels.connect,
      cancel: ConnectAccountLabels.cancel,
      close: ConnectAccountLabels.close
    },
    onClose: jest.fn(),
    onConnect: jest.fn(),
    pending: false,
    pages: [
      {
        accessToken: "test",
        id: "s1556",
        name: "Test User"
      }
    ],
    selectedPage: null,
    onChange: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows connect facebook pages modal", () => {
    renderPage(<ConnectFacebookPagesModal {...connectFacebookPagesModalProps} />);

    expect(screen.getByRole("heading", { name: connectFacebookPagesModalProps.labels.title })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: connectFacebookPagesModalProps.labels.cancel })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: connectFacebookPagesModalProps.labels.close })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: connectFacebookPagesModalProps.labels.connect })).toBeInTheDocument();
    expect(screen.getByRole("radio", { name: connectFacebookPagesModalProps.pages[0].name })).toBeInTheDocument();
    expect(screen.getByRole("button", { name: connectFacebookPagesModalProps.labels.connect })).toBeDisabled();
  });

  it("executes cancel handler when 'Cancel' button is clicked", async () => {
    renderPage(<ConnectFacebookPagesModal {...connectFacebookPagesModalProps} />);

    await userEvent.click(screen.getByRole("button", { name: connectFacebookPagesModalProps.labels.cancel }));

    expect(connectFacebookPagesModalProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("enables connect button when selected page is provided", () => {
    renderPage(
      <ConnectFacebookPagesModal
        {...connectFacebookPagesModalProps}
        selectedPage={{ pageId: "s1556", pageAccessToken: "test" }}
      />
    );

    expect(screen.getByRole("button", { name: connectFacebookPagesModalProps.labels.connect })).toBeEnabled();
  });

  it("executes connect handler when 'Connect' button is clicked", async () => {
    renderPage(
      <ConnectFacebookPagesModal
        {...connectFacebookPagesModalProps}
        selectedPage={{ pageId: "s1556", pageAccessToken: "test" }}
      />
    );

    await userEvent.click(screen.getByRole("button", { name: connectFacebookPagesModalProps.labels.connect }));

    expect(connectFacebookPagesModalProps.onConnect).toHaveBeenCalledTimes(1);
  });

  it("disables 'Cancel' and 'Close' buttons", () => {
    renderPage(<ConnectFacebookPagesModal {...connectFacebookPagesModalProps} pending={true} />);

    expect(screen.getByRole("button", { name: connectFacebookPagesModalProps.labels.close })).toBeDisabled();
    expect(screen.getByRole("button", { name: connectFacebookPagesModalProps.labels.cancel })).toBeDisabled();
  });
});
