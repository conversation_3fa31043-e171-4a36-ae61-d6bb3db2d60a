import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import ExplorePages, { ExplorePagesProps } from "./ExplorePage";
import Random from "../../test/factories/Random";

describe("Explore Pages", () => {
  const title = Random.string();
  const explorePagesProps: ExplorePagesProps = {
    title: title,
    explorePages: [
      {
        image: Random.imageUrl(),
        title: Random.string(),
        actionLabel: Random.string(),
        href: "/page1"
      },
      {
        image: Random.imageUrl(),
        title: Random.string(),
        actionLabel: Random.string(),
        href: "/page2"
      }
    ]
  };

  it("should render the title", async () => {
    render(<ExplorePages {...explorePagesProps} />);

    expect(await screen.findByText(title)).toBeInTheDocument();
  });

  it("should render the correct number of explore items", () => {
    render(<ExplorePages {...explorePagesProps} />);

    expect(screen.getAllByRole("heading", { level: 4 })).toHaveLength(explorePagesProps.explorePages.length);
  });

  it("should render the correct titles, action labels, and hrefs for explore items", async () => {
    const pageRight = explorePagesProps.explorePages[0];
    const pageLeft = explorePagesProps.explorePages[1];
    render(<ExplorePages {...explorePagesProps} />);

    waitFor(() => {
      expect(screen.getByText(pageRight.title)).toBeInTheDocument();
      expect(screen.getByText(pageRight.actionLabel)).toBeInTheDocument();
      const rightPageLink = screen.getByText(pageRight.actionLabel).closest("a");
      expect(rightPageLink).toHaveAttribute("href", pageRight.href);
      expect(screen.getByText(pageLeft.title)).toBeInTheDocument();
      expect(screen.getByText(pageLeft.actionLabel)).toBeInTheDocument();
      const leftPageLink = screen.getByText(pageLeft.actionLabel).closest("a");
      expect(leftPageLink).toHaveAttribute("href", pageLeft.href);
    });
  });
});
