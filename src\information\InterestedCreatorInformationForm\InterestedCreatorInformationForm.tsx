import React, { FC, memo, MouseEventHand<PERSON>, use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import Form from "../../utils/Form";
import Footer from "../../Footer/Footer";
import { Controller, ControllerRenderProps, FieldValues, useFormContext } from "react-hook-form";
import { DateInput, Input, MultiSelect, Select, SvgProps } from "@eait-playerexp-cn/core-ui-kit";
import { NextRouter } from "next/router";
import cx from "classnames";
import {
  ConnectAccountLabels,
  Countries,
  Languages,
  Locale,
  Locales
} from "../InterestedCreatorsInformationPage/InterestedCreatorsInformationPage";
import { Configuration, ConnectAccounts, Fbpages, Information, Rules } from "../Information";
import {
  BrowserAnalytics,
  COMPLETED_ONBOARDING_STEPS,
  ConnectedAccount,
  Dispatch,
  ErrorHandling,
  isAdult,
  isEmpty,
  NormalizedLocale,
  State,
  useAsync,
  useIsMounted
} from "../../utils/index";
import InterestedCreatorsService from "../../Browser/InterestedCreatorsService";
import SubmittedContentService from "../../Browser/SubmittedContentService";
import { LocalizedDate } from "@eait-playerexp-cn/client-kernel";
import { ConnectAccountsForm } from "../ConnectAccounts/ConnectAccountsForm/ConnectAccountsForm";
import ConnectFacebookPagesModal from "../ConnectAccounts/ConnectAccountsForm/ConnectFacebookPagesModal";
import ConnectedAccountsService from "../../Browser/ConnectedAccountsService";

const GET_FB_PAGES = "GET_FB_PAGES";
const RELOAD_INTERESTED_CREATOR_ACCOUNTS = "RELOAD_INTERESTED_CREATOR_ACCOUNTS";
const SHOW_FACEBOOK_PAGES = "SHOW_FACEBOOK_PAGES";
import AdditionalContentAndWebsiteLinks from "../AdditionalContentAndWebsiteLinks/AdditionalContentAndWebsiteLinks";

type ScanResult = {
  url: string;
  isSecure: boolean;
};

export type FormLabels = {
  infoTitle: string;
  firstName: string;
  eaEmailID: string;
  creatorEmail: string;
  lastName: string;
  dateOfBirth: string;
  preferredEmail: string;
  country: string;
  contentMediaTitle: string;
  contentMediaDescription: string;
  contentUrlPlaceholder: string;
  contentUrl: string;
  contentFollowers: string;
  contentFollowersPlaceholder: string;
  addAnother: string;
  contentLanguagesTitle: string;
  contentLanguagesDescription: string;
  contentLanguage: string;
  languageTitle: string;
  languageDescription: string;
  language: string;
  cancel: string;
  next: string;
  duplicateUrl: string;
  urlScanFailed: string;
  followersMaxLength: string;
  selectCountry: string;
  remove: string;
  ok: string;
  calendar: string;
  close: string;
  connectSocialMediaAccountTitle: string;
  connectSocialMediaAccountDescription: string;
  additionalContentAndWebsiteTitle: string;
  additionalContentAndWebsiteDescription: string;
  websiteUrlLabel: string;
  additionalLinkPlaceholder: string;
  addMoreUrlLabel: string;
  invalidUrl: string;
  ageMustBe18OrOlder: string;
};

export type InterestedCreatorInformationInputsProps = {
  formLabels: FormLabels;
  countries: Countries;
  languages: Languages;
  locales: Locales;
  interestedCreator?: Information;
  locale: string;
  rules: Rules;
  scanResults: ScanResult[] | null;
  onClose?: MouseEventHandler<HTMLButtonElement>;
  isPending: boolean;
  defaultPreferredLanguage: Locale;
  invalidUrlErrors: string[];
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  connectAccountLabels: ConnectAccountLabels;
  accountToRemove: string;
  setAccountToRemove: (accountToRemove: string) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  accounts: Array<ConnectedAccount>;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  state: State;
  errorHandling: ErrorHandling;
  stableDispatch: Dispatch;
  configuration: Configuration;
  connectAccounts: ConnectAccounts;
} & Fbpages;

export type InterestedCreatorInformationFormProps = {
  formLabels: FormLabels;
  countries: Countries;
  languages: Languages;
  locales: Locales;
  interestedCreator: Information;
  rules: Rules;
  router: NextRouter;
  redirectedToNextStepUrl: string;
  locale: string;
  onClose?: MouseEventHandler<HTMLButtonElement>;
  stableDispatch: Dispatch;
  analytics: BrowserAnalytics;
  state: State;
  errorHandling: ErrorHandling;
  configuration: Configuration;
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  connectAccountLabels: ConnectAccountLabels;
  accountToRemove: string;
  setAccountToRemove: (accountToRemove: string) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  accounts: Array<ConnectedAccount>;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  connectAccounts: ConnectAccounts;
} & Fbpages;

const InterestedCreatorInformationInputs = memo(function InterestedCreatorInformationInputs({
  formLabels,
  countries = [],
  languages = [],
  interestedCreator,
  locales = [],
  locale,
  rules,
  scanResults,
  onClose,
  isPending,
  defaultPreferredLanguage,
  invalidUrlErrors,
  showAddConfirmation,
  setShowAddConfirmation,
  connectAccountLabels,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  accounts,
  pages,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  state,
  errorHandling,
  stableDispatch,
  configuration,
  connectAccounts
}: InterestedCreatorInformationInputsProps) {
  const methods = useFormContext();
  const { showFacebookPages = false } = state as { showFacebookPages: boolean };
  const { control, reset, setError, formState, clearErrors, setValue } = methods;
  const [showPagesModal, setShowPagesModal] = useState(false);
  const [selectedPage, setSelectedPage] = useState<{ pageId: string; pageAccessToken: string } | null>(null);

  const connectedAccountsService = new ConnectedAccountsService(configuration.applicationsClient);

  useEffect(() => {
    if (scanResults?.length) {
      scanResults.map(({ isSecure }, index) => {
        if (isSecure) {
          clearErrors(`contentUrls[${index}].url`);
        } else {
          setError(`contentUrls[${index}].url`, { type: "manual", message: formLabels.urlScanFailed });
        }
      });
    } else if (invalidUrlErrors.length) {
      invalidUrlErrors.map((value) => {
        const pos = value.substring(5, 6);
        setError(`contentUrls[${pos}].url`, { type: "manual", message: formLabels.invalidUrl });
      });
    }
  }, [scanResults, setError, invalidUrlErrors]);

  useEffect(() => {
    if (!isEmpty(interestedCreator as Information)) {
      const {
        dateOfBirth = new Date().toISOString(),
        country = {},
        firstName,
        lastName,
        contentUrls = [{ url: "", followers: "" }],
        contentLanguages,
        preferredLanguage,
        originEmail
      } = interestedCreator as Information;
      reset({
        dateOfBirth: LocalizedDate.fromFormattedDate(dateOfBirth).toDate(),
        country: !isEmpty(country) ? country : countries?.[0] || {},
        firstName,
        lastName,
        originEmail,
        contentUrls,
        contentLanguages,
        preferredLanguage
      });
    }
  }, [interestedCreator, countries, reset]);

  const onSelectChange = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (field: ControllerRenderProps) => (item: any) => (item.value ? field.onChange(item) : field.onChange("")),
    []
  );
  const isMounted = useIsMounted();
  useEffect(
    function showAllFacebookPages() {
      if (pages?.length) {
        if (isMounted() && showFacebookPages) {
          setShowPagesModal(true);
        }
      } else {
        if (isMounted()) {
          setShowPagesModal(false);
        }
      }
    },
    [pages?.length, isMounted, showFacebookPages]
  );
  const onCloseFb = useCallback(async () => {
    try {
      setShowPagesModal(false);
      setSelectedPage(null);
      // Unset FBPages from session.
      await connectedAccountsService.clearFbPages();
    } catch (e) {
      errorHandling(stableDispatch, e as unknown as Error);
    }
    stableDispatch({ type: GET_FB_PAGES, data: true });
    stableDispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: true });
  }, [stableDispatch]);

  const onChange = ({ target: { id: pageId, value: pageAccessToken } }: { target: { id: string; value: string } }) =>
    setSelectedPage({ pageId, pageAccessToken });

  const onConnectFbC = useCallback(async () => {
    if (selectedPage) {
      try {
        await connectedAccountsService.connectFbPages(selectedPage);
        setSelectedPage(null);
        setShowPagesModal(false);
        stableDispatch({ type: SHOW_FACEBOOK_PAGES, data: false });
      } catch (error) {
        errorHandling(stableDispatch, error as unknown as Error);
      }
      await connectedAccountsService.clearAccountType();
      stableDispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: true });
    }
  }, [selectedPage, stableDispatch]);

  const { pending, execute: onConnectFb } = useAsync(onConnectFbC, false);

  const buttons = useMemo(() => ({ cancel: formLabels.cancel, next: formLabels.next }), [formLabels]);
  const hasAtleastOneAccountConnected = () => {
    if (INTERESTED_CREATOR_REAPPLY_PERIOD) {
      const isConnectedAccount = accounts.find((account) => account.isExpired == false);
      if (!isConnectedAccount) return false;
    }
    return accounts.length;
  };
  const disableSubmit =
    Object.keys(formState.errors).length !== 0 ||
    formState.isValid === false ||
    isPending ||
    !hasAtleastOneAccountConnected() ||
    false;

  const connectFaceBookModalLabels = {
    title: connectAccountLabels.modalConfirmationTitleFB,
    cancel: connectAccountLabels.cancel,
    connect: connectAccountLabels.connect,
    close: connectAccountLabels.close
  };

  return (
    <>
      <div className="mg-ic-ui-form-container interested-creators-ui-information-container">
        <div className="information interested-creators-ui-information">
          <h4 className="information-title">{formLabels.infoTitle}</h4>
        </div>
        <div className="information-form">
          <Controller
            control={control}
            name="firstName"
            rules={rules?.firstName}
            defaultValue={interestedCreator?.firstName}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={error?.message || ""}
                {...field}
                label={formLabels.firstName}
                placeholder={formLabels.firstName}
                id="firstName"
              />
            )}
          />
          <Controller
            control={control}
            name="lastName"
            rules={rules?.lastName}
            defaultValue={interestedCreator?.lastName}
            render={({ field, fieldState: { error } }) => (
              <Input
                errorMessage={error?.message || ""}
                {...field}
                label={formLabels.lastName}
                placeholder={formLabels.lastName}
                id="lastName"
              />
            )}
          />
          <Controller
            control={control}
            name="dateOfBirth"
            rules={rules?.dateOfBirth}
            defaultValue={LocalizedDate.fromFormattedDate(interestedCreator?.dateOfBirth ?? "").toDate()}
            render={({ field, fieldState: { error } }) => (
              <DateInput
                errorMessage={error?.message || ""}
                {...field}
                label={formLabels.dateOfBirth}
                placeholder={formLabels.dateOfBirth}
                locale={locale}
                maxDate={new Date()}
                title={formLabels.calendar}
                cancelText={formLabels.cancel}
                okText={formLabels.ok}
                onCancel={(date: Date) => {
                  if (isAdult(date.toDateString())) {
                    setError(
                      "dateOfBirth",
                      { type: "manual", message: formLabels.ageMustBe18OrOlder },
                      { shouldFocus: true }
                    );
                  } else {
                    setError("dateOfBirth", {});
                  }
                  setValue("dateOfBirth", date);
                }}
              />
            )}
          />
          <div className="information-label">
            <span className="input-box-label">{formLabels.eaEmailID}</span>
            <h4 className="information-label-creator-email">{interestedCreator?.originEmail}</h4>
          </div>

          <Controller
            control={control}
            name="preferredEmail"
            rules={rules?.preferredEmail}
            defaultValue={interestedCreator?.originEmail}
            render={({ field, fieldState: { error } }) => (
              <Input
                id="email"
                errorMessage={error?.message || ""}
                {...field}
                label={formLabels.preferredEmail}
                placeholder={formLabels.preferredEmail}
              />
            )}
          />
          <Controller
            control={control}
            name="country"
            rules={rules?.interestedCreatorCountry}
            render={({ field, fieldState: { error } }) => (
              <Select
                id="creator-country"
                selectedOption={interestedCreator?.country}
                errorMessage={error?.message}
                onChange={onSelectChange(field)}
                options={countries}
                label={formLabels.country}
                dark
              />
            )}
          />
        </div>
      </div>

      <>
        <div className="mg-ic-ui-communication-row content-in-center content-language">
          <h4 className="information-title content-language">{formLabels.contentLanguagesTitle}</h4>
          <div className="mg-ic-ui-communication-description content-language">
            {formLabels.contentLanguagesDescription}
          </div>
          <Controller
            control={control}
            name="contentLanguages"
            rules={rules?.contentLanguage}
            render={({ field, fieldState: { error } }) => (
              <MultiSelect
                {...field}
                selectedOptions={field.value}
                options={languages}
                errorMessage={error?.message || ""}
                placeholder={formLabels.contentLanguage}
                label={formLabels.contentLanguage}
              />
            )}
          />
        </div>

        <div className="mg-ic-ui-communication-row content-in-center language">
          <h4 className="information-title language">{formLabels.languageTitle}</h4>
          <div className="mg-ic-ui-communication-description language">{formLabels.languageDescription}</div>
          <Controller
            control={control}
            name="preferredLanguage"
            defaultValue={defaultPreferredLanguage}
            render={({ field, fieldState: { error } }) => (
              <Select
                id="creator-preferred-language"
                {...field}
                selectedOption={defaultPreferredLanguage}
                errorMessage={error && error.message}
                options={locales}
                dark
                label={formLabels.language}
              />
            )}
          />
        </div>

        <div className="mg-ic-ui-communication-row content-in-center ic-ui-add-account-container">
          <h5 className="information-title language add-account-social-media-header" id="ic-ui-connect-accounts">
            {formLabels.connectSocialMediaAccountTitle}
          </h5>
          <div className="add-account-description">{formLabels.connectSocialMediaAccountDescription}</div>
          <div
            className={cx("content-in-center ic-ui-myprofile-view", {
              "ic-ui-add-account-card": accounts?.length === 1
            })}
            aria-labelledby="ic-ui-connect-accounts"
            data-testid="ic-ui-add-account-card"
          >
            <ConnectAccountsForm
              labels={connectAccountLabels}
              setShowAddConfirmation={setShowAddConfirmation}
              accountToRemove={accountToRemove}
              showAddConfirmation={showAddConfirmation}
              setAccountToRemove={setAccountToRemove}
              accounts={accounts}
              showRemoveAccountModal={showRemoveAccountModal}
              setShowRemoveAccountModal={setShowRemoveAccountModal}
              state={state}
              errorHandling={errorHandling}
              stableDispatch={stableDispatch}
              configuration={configuration}
              connectAccounts={connectAccounts}
            />
            {showPagesModal && (
              <ConnectFacebookPagesModal
                labels={connectFaceBookModalLabels}
                onClose={onCloseFb}
                onChange={onChange}
                pages={pages}
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                onConnect={onConnectFb as any}
                pending={pending}
                selectedPage={selectedPage}
              />
            )}
          </div>
        </div>

        <div
          className="mg-ic-ui-communication-row content-in-center additional-website-links"
          id="interested-creators-ui-information-additional-content-container"
        >
          <AdditionalContentAndWebsiteLinks {...{ labels: formLabels, rules }} />
        </div>
      </>

      <Footer {...{ buttons, onCancel: onClose, disableSubmit, isPending }} />
    </>
  );
});

export type NavStep = {
  icon: FC<SvgProps>;
  title: string;
  href: string;
  isCompleted?: boolean;
};

export default memo(function InterestedCreatorInformationForm({
  formLabels,
  languages,
  locales,
  countries,
  interestedCreator,
  rules,
  router,
  locale,
  stableDispatch,
  onClose,
  analytics,
  redirectedToNextStepUrl,
  state,
  errorHandling,
  configuration,
  showAddConfirmation,
  setShowAddConfirmation,
  connectAccountLabels,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  accounts,
  pages,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  connectAccounts
}: InterestedCreatorInformationFormProps) {
  const [scanResults, setScanResults] = useState<Array<ScanResult>|null>(null);
  const [invalidUrlErrors, setInvalidUrlErrors] = useState([]);
  const { onboardingSteps } = state as {onboardingSteps: Array<NavStep>};
  const defaultValues = useMemo(() => {
    const {
      dateOfBirth = new Date().toISOString(),
      country = {},
      firstName,
      lastName,
      contentUrls = [{ url: "", followers: "" }],
      contentLanguages,
      preferredLanguage = {
        code: "",
        name: ""
      },
      originEmail
    } = interestedCreator;
    return {
      dateOfBirth: LocalizedDate.fromFormattedDate(dateOfBirth).toDate(),
      country: !isEmpty(country) ? country : countries?.[0] || {},
      firstName,
      lastName,
      contentUrls,
      contentLanguages,
      preferredLanguage,
      originEmail
    };
  }, [interestedCreator, countries]);
  const interestedCreatorsService = new InterestedCreatorsService(configuration.applicationsClient)
  const submittedContentServices = new SubmittedContentService(configuration.applicationsClient)
 
  /* To get preferred locale */
  const getPreferredLocale = useCallback(
    (preferredLanguage:{code:string,name:string}): Locale => {
      const localeLanguageCode = router.locale && NormalizedLocale.fromSlug(router.locale).toString();
      return locales.find(({ value }) => {
        return (
          value ===
          (preferredLanguage && preferredLanguage?.code && preferredLanguage?.code !== ""
            ? preferredLanguage?.code
            : localeLanguageCode)
        );
      }) as Locale;
    },
    [router, locales]
  );
  const defaultPreferredLanguage: Locale = getPreferredLocale(defaultValues.preferredLanguage);
  const handleSaveInformationForm = async (data:FieldValues, currentStep:NavStep) => {
    const { preferredEmail, preferredLanguage } = data;
    const payload = {
      ...data,
      nucleusId: interestedCreator.nucleusId,
      defaultGamerTag: interestedCreator.defaultGamerTag,
      originEmail: interestedCreator.originEmail,
      preferredEmail: preferredEmail,
      dateOfBirth: LocalizedDate.fromFormattedDate(data.dateOfBirth).format("YYYY-MM-DD"),
      preferredLanguage: { code: preferredLanguage?.value, name: preferredLanguage?.label }
    };
    try {
      await interestedCreatorsService.saveApplication(payload);
    } catch (e) {
      errorHandling(stableDispatch, e as unknown as Error);
      return;
    }
    if(analytics.continuedCreatorApplication) { 
      analytics.continuedCreatorApplication({ locale: router.locale ?? '', page: location.pathname, finalStep: false })
    }
    stableDispatch({ type: COMPLETED_ONBOARDING_STEPS, data: { currentStep } });
    router.push(redirectedToNextStepUrl);
  };

  const handleSubmitWithAddAccountEnabled = async (contentUrls:[], data:FieldValues, currentStep:NavStep) => {
    const urls:[] = [];
    // Convert form values to an array of URLs
    contentUrls.forEach(({ url }) => url && url !== "https://" && urls.push(url));
    if (urls.length > 0) {
      try {
        const response = await submittedContentServices.validateContent(urls, "INTERESTED_CREATORS");
        const results = response.results;
        const scanFailed = !!results.find((result:{isSecure: boolean}) => !result.isSecure);
        if (scanFailed) {
          setScanResults(results);
          return;
        }
        await handleSaveInformationForm(data, currentStep);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (e:any) {
        setScanResults(null);
        if (e.response.status === 422 && e.response.data.code === "validate-content-urls-invalid-input") {
          setInvalidUrlErrors(e.response.data.errors && Object.keys(e.response.data.errors));
          return;
        }
        errorHandling(stableDispatch, e);
      }
    } else {
      await handleSaveInformationForm(data, currentStep);
    }
  };

  const onSubmit = useCallback(
    async <T extends FieldValues>(data: T) => {
      const { contentUrls = [] } = data;
      const currentStep = onboardingSteps?.find((step) => step.href === router.pathname);
       await handleSubmitWithAddAccountEnabled(contentUrls, data, (currentStep as NavStep));
    },
    [
      interestedCreator.nucleusId,
      interestedCreator.defaultGamerTag,
      locale,
      router,
      stableDispatch,
      defaultPreferredLanguage,
      onboardingSteps
    ]
  );
  const { pending: isPending, execute: submitHandler } = useAsync<FieldValues, void>(onSubmit, false);

  return (
    <Form mode="onChange" onSubmit={submitHandler} defaultValues={defaultValues}>
      <InterestedCreatorInformationInputs
        {...{
          formLabels,
          countries,
          languages,
          locales,
          locale,
          rules,
          interestedCreator,
          scanResults,
          onClose,
          isPending,
          defaultPreferredLanguage,
          invalidUrlErrors,
          showAddConfirmation,
          setShowAddConfirmation,
          connectAccountLabels,
          accountToRemove,
          setAccountToRemove,
          showRemoveAccountModal,
          setShowRemoveAccountModal,
          accounts,
          pages,
          INTERESTED_CREATOR_REAPPLY_PERIOD,
          state,
          errorHandling,
          stableDispatch,
          configuration,
          connectAccounts
        }}
      />
    </Form>
  );
});
