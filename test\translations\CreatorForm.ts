export const creatorFormLabels = {
  messages: {
    firstName: "First Name is required",
    firstNameTooLong: "First Name is too long",
    lastName: "Last Name is required",
    lastNameTooLong: "Last Name is too long",
    dateOfBirth: "Date of Birth is required",
    dateOfBirthInvalid: "Date of Birth is invalid",
    ageMustBe18OrOlder: "Must be 18 or older",
    country: "Country/Region is required",
    street: "Street is required",
    streetTooLong: "Street is too long",
    city: "City is required",
    cityTooLong: "City is too long",
    state: "State or Province is required",
    stateTooLong: "State is too long",
    zipCode: "Zip Code or Postal Code is required",
    zipCodeTooLong: "Zip Code or Postal Code is too long",
    tShirtSize: "T-Shirt Size is required",
    entityType: "Entity Type is required",
    businessName: "Business Name is required",
    businessNameTooLong: "Business Name is too long",
    email: "Email Address is required",
    emailTooLong: "Email Address is too long",
    emailInvalid: "Email Address is invalid",
    url: "URL is required",
    invalidUrl: "URL provided is invalid",
    followersMaxLength: "Maximum 18 digits allowed"
  },
  CommunicationForm: {
    messages: {
      preferredEmail: "Preferred email is required",
      preferredEmailTooLong: "Preferred email is too long",
      preferredEmailInvalid: "Invalid email",
      preferredPhoneNumber: "Preferred phone number is required",
      preferredPhoneNumberTooLong: "Preferred phone number is too long",
      contentLanguage: "Content language is required",
      language: "Language is required"
    }
  },
  PactSafeForm: {
    messages: {
      businessNameRequired: "Business name is required"
    }
  }
};
