import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import { ConnectedAccount } from "../utils";

export type FacebookPage = {
  id: string;
  accessToken: string;
  name: string;
};

type Pages = {
  pages: Array<FacebookPage>;
};

export type ErrorType = {
  code: string;
  message: string;
};

type SelectedPage = {
  pageId: string;
  pageAccessToken: string;
};

class ConnectedAccountsService {
  constructor(private readonly client: TraceableHttpClient) {}

  clearAccountType = async (): Promise<void> => {
    await this.client.delete("/api/account-types");
  }; //

  clearFbPages = async (): Promise<void> => {
    await this.client.delete("/api/facebook-connect");
  }; //

  connectFbPages = async (selectedPage: SelectedPage): Promise<void> => {
    await this.client.post("/api/facebook-channels", { body: selectedPage });
  }; //

  getAllConnectedAccountsWithExpirationStatus = async (nucleusId: number): Promise<Array<ConnectedAccount>> => {
    const response = await this.client.get("/api/v2/connected-accounts", { query: { nucleusId } });
    return response.data;
  };

  getFacebookPages = async (): Promise<Pages> => {
    const response = await this.client.get("/api/facebook-pages");
    return response.data;
  };

  getConnectedAccounts = async (nucleusId: number): Promise<Array<ConnectedAccount>> => {
    const response = await this.client.get("/api/connected-accounts", { query: { nucleusId } });
    return response.data;
  };

  removeConnectedAccount = async (accountId: string): Promise<void> => {
    await this.client.delete(`/api/accounts/${accountId}`);
  }; //

  getConnectAccountErrors = async (): Promise<ErrorType> => {
    const response = await this.client.get("/api/errors");
    return response.data;
  };

  deleteConnectedAccountErrors = async (): Promise<ErrorType> => {
    const response = await this.client.delete("/api/sessions/error");
    return response.data;
  };
}

export default ConnectedAccountsService;
