import CheckboxCards from "./CheckboxCards";
import { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import React, { ComponentType, ReactElement } from "react";

/**
 * Collection of checkbox cards
 */
const meta: Meta<typeof CheckboxCards> = {
  title: "Component Library/Checkbox Cards",
  component: CheckboxCards,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof CheckboxCards>;

/**
 * Only with unchecked elements
 */
export const Unchecked: Story = {
  args: {
    items: [
      {
        label: "Fifa",
        value: "fifa",
        checked: false,
        image:
          "https://media.contentapi.ea.com/content/dam/gin/images/2020/06/fifa21-keyart-horizontal-16x9.png.adapt.crop1x1.767p.png"
      },
      {
        label: "Plants vs Zombies",
        value: "plants-vs-zombies",
        checked: false,
        image:
          "https://media.contentapi.ea.com/content/dam/gin/images/2019/09/plants-vs-zombies-battle-for-neighborville-keyart.jpg.adapt.crop1x1.767p.jpg"
      },
      {
        label: "Apex Legends",
        value: "apex-legends",
        checked: false,
        image:
          "https://media.contentapi.ea.com/content/dam/gin/images/2019/01/apex-legends-keyart.jpg.adapt.crop1x1.767p.jpg"
      }
    ],
    basePath: "/support-a-creator"
  }
};

/**
 * Only with checked elements
 */
export const Checked: Story = {
  args: {
    items: [
      {
        label: "Star Wars",
        value: "star-wars",
        checked: true,
        image: "https://media.contentapi.ea.com/content/dam/gin/images/2020/05/sws-keyart.jpg.adapt.crop1x1.767w.jpg"
      },
      {
        label: "Fifa",
        value: "fifa",
        checked: true,
        image:
          "https://media.contentapi.ea.com/content/dam/gin/images/2020/06/fifa21-keyart-horizontal-16x9.png.adapt.crop1x1.767p.png"
      },
      {
        label: "Plants vs Zombies",
        value: "plants-vs-zombies",
        checked: true,
        image:
          "https://media.contentapi.ea.com/content/dam/gin/images/2019/09/plants-vs-zombies-battle-for-neighborville-keyart.jpg.adapt.crop1x1.767p.jpg"
      }
    ],
    basePath: "/support-a-creator"
  }
};
