.start-wrapper {
  @apply flex flex-col items-center justify-center text-white text-center w-[290px] md:w-[630px] mx-auto;
}
.start-content-container {
  @apply text-center mx-auto;
}
.start-apply-button > .btn {
  @apply mt-meas16;
}
.start-title {
  @apply my-meas16 font-display-bold xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 text-gray-10 md:text-white;
}
.start-sub-title {
  @apply font-display-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4 text-gray-10 md:text-white;
  word-break: break-word;
}
.start-body {
  @apply mt-meas16 pb-meas16 text-center font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large text-gray-10
  border-b-[1px] border-[rgba(255,255,255,0.33)];
}
.start-ea-account,
.interested-creators-ui-already-applied {
  @apply underline cursor-pointer;
}
.start-already-applied-content-container {
  @apply my-meas16 text-center font-text-regular xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large text-gray-10;
}
.start-thumbnail-container {
  @apply border-b-[1px] border-[rgba(255,255,255,0.33)] w-full mt-meas6 md:mt-[90px] lg:mt-[70px];
}
.start-thumbnail {
  @apply min-h-[199px] md:min-h-[325px] max-w-[290px] md:max-w-[473px] bg-cover bg-no-repeat mx-auto;
  background-position: 0;
}
