import { Country, Language } from "@eait-playerexp-cn/metadata-types";

export type BrowserAnalytics = {
  startedCreatorApplication?: ({ locale, page }: { page: string; locale: string }) => void;
  checkedApplicationStatus?: ({ locale, status }: { locale: string; status: string }) => void;
  continuedCreatorApplication?: ({
    locale,
    page,
    finalStep,
    creatorTypes
  }: {
    locale: string;
    page: string;
    finalStep?: boolean;
    creatorTypes?: [];
  }) => void;
  cancelledCreatorApplication?: ({ locale, page }: { locale: string; page: string }) => void;
};

export type State = {
  [key: string]: string | number | boolean | null | object | [];
};

export type CreatorType = "CREATOR" | "INTERESTED_CREATOR";

export type AuthenticatedUser = {
  analyticsId: string | undefined;
  needsMigration?: boolean;
  username: string;
  status?: string;
  avatar?: string;
  tier?: string;
  isPayable?: boolean;
  type?: CreatorType;
  isFlagged?: boolean;
};

export type Locale = {
  value: string;
  label: string;
  id?: string;
};

export type ContentUrl = {
  url: string;
  followers: number;
};

export type FranchiseType = "PRIMARY" | "SECONDARY";

export type PreferredFranchise = {
  id: string;
  type: FranchiseType;
};

export type Creator = {
  value: string;
  imageAsIcon: string;
  label: string;
  checked?: boolean;
};

export type PreferredLanguage = {
  code: string;
  name: string;
};

export type InterestedCreator = {
  defaultGamerTag: string;
  nucleusId: number;
  firstName?: string;
  lastName?: string;
  originEmail: string;
  dateOfBirth: string | number;
  preferredEmail?: string;
  country?: Country;
  creatorTypes?: Creator[];
  contentLanguages?: Language[];
  contentUrls?: ContentUrl[];
  preferredFranchises?: PreferredFranchise[];
  preferredLanguage?: PreferredLanguage;
  analyticsId: string;
  createdDate?: string;
};

export type Layout = {
  buttons: {
    gotit: string;
    submitContent: string;
    cancel: string;
    submit: string;
    upload: string;
    next: string;
    prev: string;
    close: string;
    connect: string;
  };
  toasts: { contentSubmittedTitle: string; contentSubmittedDescription: string };
  main: {
    pageNotFound: string;
    pageNotFoundContent: string;
    unauthorized: string;
    unhandledError: string;
    unhandledErrorMessage: string;
  };
  contentCard: {
    approvalNotRequired: string;
    approved: string;
    pendingApproval: string;
    rejected: string;
    submitted: string;
    video: string;
    changesRequired: string;
    inReview: string;
    viewChangesRequired: string;
    hideChangesRequired: string;
    sentOn: string;
    additionalDescription: string;
    file: string;
    url: string;
    update: string;
    from: string;
    inScan: string;
  };
  header: {
    disclosure: string;
  };
};

export type ErrorHandling = (dispatch: Dispatch, error: Error) => void;

export type Dispatch = (action: { type: string; data: string | number | boolean | null | object | [] }) => void;

export type ValidationError = {
  propertyName?: string;
  errorMessages?: string[] | string;
};

export type CountryResponse = { code: string; name: string; value?: string; label?: string };

export type ScannedUrl = {
  url: string;
  isSecure: boolean;
};

export type ContentScanResult = {
  results: Array<ScannedUrl>;
};

export type CreatorResponse = { id: string; boxArtUrl: string };

export type FranchiseResponse = { id: string; name: string; boxArtUrl: string; type: string };

export type LanguageResponse = { code: string; name: string; id: string; value?: string; label?: string };

export type LocaleResponse = { code: string; name: string; id: string; value?: string; label?: string };
export class NormalizedLocale {
  private readonly language: string;
  private readonly country: string;

  constructor(language: string, country: string) {
    this.language = language.toLowerCase();
    this.country = country.toUpperCase();
  }

  static fromSlug(slug: string): NormalizedLocale {
    const [language, country] = slug.split("-");
    return new NormalizedLocale(language, country);
  }

  toString(): string {
    return `${this.language}_${this.country}`;
  }
}

export type CloseHandler = {
  onClose: () => void;
};

export type ChannelType = "YOUTUBE" | "INSTAGRAM" | "TWITCH" | "FACEBOOK" | "TIKTOK";

export type ConnectedAccount = {
  id: string;
  accountId: string;
  name: string;
  type: ChannelType;
  uri: string;
  thumbnail: string;
  username: string;
  disconnected: boolean;
  isExpired: boolean;
};
