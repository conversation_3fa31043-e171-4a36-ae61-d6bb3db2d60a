import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import ExplorePages from "./ExplorePage";
import React, { ComponentType, ReactElement } from "react";

const meta: Meta<typeof ExplorePages> = {
  title: "Component Library/Explore Pages",
  component: ExplorePages,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof ExplorePages>;

export const Explore: Story = {
  args: {
    title: "Explore what the EA Creator Network has to Offer",
    explorePages: [
      {
        title: "How does the new platform work?",
        image: "/img/home-how-does-platform-work--650w-x-650h.png",
        actionLabel: "How it Works",
        href: "/how-it-works"
      },
      {
        title: "Opportunities, communities and partnerships",
        image: "/img/home-opportunities-communities-partnerships--650w-x-650h.png",
        actionLabel: "Available Perks",
        href: "/opportunities-rewards"
      }
    ]
  }
};
