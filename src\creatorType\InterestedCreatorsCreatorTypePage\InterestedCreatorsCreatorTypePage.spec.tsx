import React from "react";
import { render, screen } from "@testing-library/react";
import { NextRouter, useRouter } from "next/router";
import { axe } from "jest-axe";
import { aCreatorType } from "@eait-playerexp-cn/metadata-test-fixtures";
import { creatorTypeLabel } from "../../../test/translations/CreatorType";
import CreatorForm from "../../utils/CreatorForm";
import { informationLabels } from "../../../test/translations/Information";
import { communicationPreferencesLabels } from "../../../test/translations/CommunicationPreferences";
import { aLocalizedDate } from "../../../test/factories/LocalizedDateBuilderFactories";
import { interestedCreatorsCreatorTypeFormLabels } from "../../../test/translations/InterestedCreatorsCreatorTypeForm";
import { BrowserAnalytics } from "../../utils";
import { Configuration, Rules } from "../../information/Information";
import InterestedCreatorsCreatorTypePage from "./InterestedCreatorsCreatorTypePage";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));

describe("InterestedCreatorsCreatorTypePage", () => {
  const creatorTypes = [
    aCreatorType({ value: "YOUTUBER", label: "YouTuber" }),
    aCreatorType({ value: "BLOGGER", label: "Blogger" }),
    aCreatorType({ value: "LIFESTYLE", label: "lifestyle" })
  ];
  const { FormLabels: formLabels, PageLabels: pageLabels } = creatorTypeLabel;
  const allRules = {
    ...CreatorForm.rules(informationLabels),
    ...CreatorForm.communicationRules(communicationPreferencesLabels)
  };
  const dateOfBirthAfter18years = aLocalizedDate().minusYears(18).build().formatWithEpoch("YYYY-MM-DD");
  const { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url } = allRules;
  const rules: Rules = { firstName, lastName, dateOfBirth, country, preferredEmail, contentLanguage, url };
  let router: NextRouter;
  const configuration = { metadataClient: {}, applicationsClient: {} } as Configuration;
  const interestedCreatorsCreatorTypePageProps = {
    formLabels: { ...formLabels, ...interestedCreatorsCreatorTypeFormLabels },
    pageLabels,
    stableDispatch: jest.fn(),
    handleCancelRegistration: jest.fn(),
    errorToast: jest.fn(),
    setShowConfirmation: jest.fn(),
    rules,
    locale: "en-us",
    state: {},
    router: useRouter(),
    analytics: {} as unknown as BrowserAnalytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    errorHandling: jest.fn(),
    redirectedToNextStepUrl: "/interested-creators/franchises-you-play",
    configuration: configuration,
    onClose: jest.fn(),
    basePath: "/support-a-creator"
  };

  beforeEach(() => {
    jest.clearAllMocks();
    router = { push: jest.fn() } as unknown as NextRouter;
    (useRouter as jest.Mock).mockImplementation(() => router);
    (MetadataService as jest.Mock).mockReturnValue({
      getCreatorTypes: jest.fn().mockResolvedValue(creatorTypes)
    });
  });

  it("shows placeholders when no creator information has been entered", async () => {
    const interestedCreator = {
      nucleusId: 1234567,
      defaultGamerTag: "RiffleShooter",
      originEmail: "<EMAIL>",
      dateOfBirth: dateOfBirthAfter18years,
      creatorTypes
    };

    render(
      <InterestedCreatorsCreatorTypePage
        {...interestedCreatorsCreatorTypePageProps}
        interestedCreator={interestedCreator}
      />
    );

    expect(await screen.findByText(/Choose your Creator Types/i)).toBeInTheDocument();
    expect(
      await screen.findByText(/Select the types of creator that fits the content you create./i)
    ).toBeInTheDocument();
  });

  describe("with 'INTERESTED_CREATOR_REAPPLY_PERIOD' flag enabled", () => {
    it("pre-populates checkboxes values", async () => {
      const interestedCreator = {
        nucleusId: 1234567,
        defaultGamerTag: "RiffleShooter",
        originEmail: "<EMAIL>",
        dateOfBirth: dateOfBirthAfter18years,
        creatorTypes: ["LIFESTYLE", "YOUTUBER"]
      };

      render(
        <InterestedCreatorsCreatorTypePage
          {...interestedCreatorsCreatorTypePageProps}
          interestedCreator={interestedCreator}
          INTERESTED_CREATOR_REAPPLY_PERIOD
        />
      );
      expect(await screen.findByText(/YouTuber/i)).toBeInTheDocument();
      expect(await screen.findByText(/lifestyle/i)).toBeInTheDocument();
    });
  });

  it("is accessible", async () => {
    const { container } = render(<InterestedCreatorsCreatorTypePage {...interestedCreatorsCreatorTypePageProps} />);
    await screen.findByRole("checkbox", { name: /YouTuber/i });
    screen.getByRole("checkbox", { name: /blogger/i });

    const results = await axe(container);

    expect(results).toHaveNoViolations();
  });
});
