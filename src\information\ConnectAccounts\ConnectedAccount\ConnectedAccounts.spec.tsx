import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { ConnectedAccount as Account } from "../../../utils";
import { ConnectAccountLabels } from "../../../../test/translations/ConnectAccounts";
import { ConnectedAccountComponent, ConnectedAccountProps } from "./ConnectedAccounts";
import { Configuration } from "../../Information";
import { facebookIcon, youTubeIcon } from "@eait-playerexp-cn/core-ui-kit";
import userEvent from "@testing-library/user-event";

jest.mock("../../../Browser/ConnectedAccountsService", () => {
  return jest.fn().mockImplementation(function () {
    return {
      removeConnectedAccount: jest.fn()
    };
  });
});

describe("ConnectedAccount Component", () => {
  const accounts: Array<Account> = [
    {
      id: "1",
      accountId: "123",
      type: "FACEBOOK",
      username: "user1",
      name: "name1",
      uri: "uri1",
      thumbnail: "thumbnail1",
      isExpired: false,
      disconnected: false
    },
    {
      id: "2",
      accountId: "456",
      type: "YOUTUBE",
      username: "user2",
      name: "name2",
      uri: "uri2",
      thumbnail: "thumbnail2",
      isExpired: false,
      disconnected: false
    }
  ];

  const configuration = { metadataClient: {}, applicationsClient: {} } as Configuration;

  const connectAccounts = [
    { value: "facebook", accountIcon: facebookIcon, redirectUrl: "http://facebook.com" },
    { value: "youtube", accountIcon: youTubeIcon, redirectUrl: "http://youtube.com" }
  ];

  const defaultProps: ConnectedAccountProps = {
    labels: ConnectAccountLabels,
    setAccountToRemove: jest.fn(),
    accountToRemove: "",
    accounts,
    setShowAddConfirmation: jest.fn(),
    showAddConfirmation: false,
    showRemoveAccountModal: false,
    setShowRemoveAccountModal: jest.fn(),
    state: {},
    errorHandling: jest.fn(),
    stableDispatch: jest.fn(),
    configuration: configuration,
    connectAccounts: connectAccounts
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("shows account connected", () => {
    render(<ConnectedAccountComponent {...defaultProps} />);

    expect(screen.getByText(ConnectAccountLabels.myAccount)).toBeInTheDocument();
    expect(screen.getByText("user1")).toBeInTheDocument();
    expect(screen.getByText("user2")).toBeInTheDocument();
  });

  it("shows close remove account modal on cancel button click", () => {
    render(<ConnectedAccountComponent {...defaultProps} showRemoveAccountModal={true} />);
    const cancelButton = screen.getByText(ConnectAccountLabels.cancel);
    expect(cancelButton).toBeInTheDocument();

    userEvent.click(cancelButton);

    waitFor(() => {
      expect(defaultProps.setShowRemoveAccountModal).toHaveBeenCalledWith(false);
    });
  });

  it("renders null when accounts array is empty", () => {
    const { container } = render(<ConnectedAccountComponent {...defaultProps} accounts={[]} />);

    expect(container.firstChild).toBeNull();
  });

  it("handles remove account action", async () => {
    render(<ConnectedAccountComponent {...defaultProps} accountToRemove="1-FACEBOOK" showRemoveAccountModal={true} />);

    userEvent.click(screen.getByText(ConnectAccountLabels.remove));

    await waitFor(() => {
      expect(defaultProps.setAccountToRemove).toHaveBeenCalledWith(null);
      expect(defaultProps.setShowRemoveAccountModal).toHaveBeenCalledWith(false);
    });
  });

  it("displays the modal with correct labels", () => {
    const modalLabels = {
      ...ConnectAccountLabels.modal,
      removeAccountTitle: "Remove Account",
      removeAccountDescription1: "Are you sure?",
      removeAccountDescription2: "This action cannot be undone"
    };

    render(
      <ConnectedAccountComponent
        {...defaultProps}
        showRemoveAccountModal={true}
        labels={{
          ...ConnectAccountLabels,
          modal: modalLabels
        }}
      />
    );

    expect(screen.getAllByText("Remove Account")[0]).toBeInTheDocument();
    expect(screen.getAllByText("Are you sure?")[0]).toBeInTheDocument();
    expect(screen.getAllByText("This action cannot be undone")[0]).toBeInTheDocument();
  });
});
