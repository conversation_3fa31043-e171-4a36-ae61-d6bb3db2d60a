import React from "react";
import { act, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { axe } from "jest-axe";
import NoAccountPage, { NoAccountProps } from "./NoAccountPage";
import { noAccountLabels } from "../../test/translations/NoAccount";

describe("NoAccountPage", () => {
  const noAccountProps: NoAccountProps = {
    labels: noAccountLabels,
    email: "<EMAIL>",
    startApplication: jest.fn(),
    noAccountThumbnail: "https://via.placeholder.com/150"
  };

  beforeEach(() => jest.clearAllMocks());

  it("shows no account found labels", () => {
    render(<NoAccountPage {...{ ...noAccountProps }} />);

    const { labels, email } = noAccountProps;

    expect(screen.getByText(labels.title)).toBeInTheDocument();
    const subTitle = screen.getByText(`${labels.subTitlePart1} ${email}. ${labels.subTitlePart2}`);
    expect(subTitle).toBeInTheDocument();
    expect(screen.getByText(labels.descriptionPara1)).toBeInTheDocument();
    expect(screen.getByText(labels.requestToJoin)).toBeInTheDocument();
  });

  it("calls startApplication function on clicking Request to Join", async () => {
    render(<NoAccountPage {...{ ...noAccountProps }} />);

    await userEvent.click(await screen.findByRole("button", { name: noAccountProps.labels.requestToJoin }));

    expect(noAccountProps.startApplication).toHaveBeenCalledTimes(1);
  });

  it("is accessible", async () => {
    let results;
    const { container } = render(<NoAccountPage {...{ ...noAccountProps }} />);

    await act(async () => {
      results = await axe(container);
    });

    expect(results).toHaveNoViolations();
  });
});
