import React, { FC } from "react";
import classnames from "classnames";
import Unreviewed from "../styles/img/icons/Unreviewed";
import RequestsToJoinStatusTable from "../RequestsToJoinStatusTable/RequestsToJoinStatusTable";
import { Button } from "@eait-playerexp-cn/core-ui-kit";

export type Labels = {
  title: string;
  subTitle: string;
  description: string;
  backHome: string;
  submissionCompleteSubTitle: string;
  submissionCompleteDescription: string;
  email: string;
  programLabel: string;
  status: string;
  submissionDate: string;
  unReviewed: string;
  creatorNetwork: string;
  close: string;
  programName: string;
};
export type ApplicationCompletedPageProps = {
  labels: Labels;
  INTERESTED_CREATOR_REAPPLY_PERIOD?: boolean;
  submittedDate?: string;
  emailId?: string;
  onBackButtonClick: () => void;
  applicationCompleteThumbnail: string;
};

const ApplicationCompletedPage: FC<ApplicationCompletedPageProps> = ({
  labels,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  submittedDate,
  emailId,
  onBackButtonClick,
  applicationCompleteThumbnail
}) => {
  const { title, subTitle, description, backHome, unReviewed } = labels;

  return (
    <div className="mg-ic-ui-page-interested-creator">
      <div className={INTERESTED_CREATOR_REAPPLY_PERIOD ? "complete-wrapper reapply" : "complete-wrapper"}>
        {!INTERESTED_CREATOR_REAPPLY_PERIOD && (
          <div className="complete-thumbnail-container">
            <img className="complete-thumbnail" src={applicationCompleteThumbnail} alt="" />
          </div>
        )}
        <div
          className={classnames("complete-content-container", {
            "complete-content-container-re-apply": INTERESTED_CREATOR_REAPPLY_PERIOD
          })}
        >
          <h3 className="complete-title">{title}</h3>
          <h4 className="complete-sub-title">{subTitle}</h4>
          <div className="complete-body">{description}</div>
        </div>
        {INTERESTED_CREATOR_REAPPLY_PERIOD && (
          <RequestsToJoinStatusTable
            requestsToJoinStatusTableLabels={labels}
            emailId={emailId ?? ""}
            INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
            submittedDate={submittedDate}
            statusIcon={Unreviewed}
            applicationStatus={unReviewed}
          />
        )}
        <div className="complete-back-home">
          <Button onClick={onBackButtonClick} type="button" variant="primary" size="md">
            {backHome}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ApplicationCompletedPage;
