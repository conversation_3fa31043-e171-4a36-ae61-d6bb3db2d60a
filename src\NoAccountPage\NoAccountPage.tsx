import React, { <PERSON> } from "react";
import { Button } from "@eait-playerexp-cn/core-ui-kit";
import { State } from "../utils/types";

export type Labels = {
  title: string;
  subTitlePart1: string;
  subTitlePart2: string;
  descriptionPara1: string;
  creatorNetwork: string;
  requestToJoin: string;
  close: string;
  noAccountThumbnailAltText: string;
};
export type NoAccountProps = {
  labels: Labels;
  email: string;
  startApplication: () => void;
  state?: State;
  noAccountThumbnail: string;
};
const NoAccountPage: FC<NoAccountProps> = ({ labels, email, startApplication, noAccountThumbnail }) => {
  const { title, subTitlePart1, subTitlePart2, descriptionPara1, requestToJoin, noAccountThumbnailAltText } = labels;

  return (
    <div className="mg-ic-ui-page-interested-creator">
      <div className="no-account-wrapper">
        <img className="no-account-thumbnail" src={noAccountThumbnail} alt={noAccountThumbnailAltText} />
        <div className="no-account-content-container">
          <h3 className="no-account-title">{title}</h3>
          <h4 className="no-account-sub-title">{`${subTitlePart1} ${email}. ${subTitlePart2}`}</h4>
          <div className="no-account-body">{descriptionPara1}</div>
        </div>
        <div className="no-account-request-to-join">
          <Button variant="primary" size="md" onClick={startApplication}>
            {requestToJoin}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NoAccountPage;
