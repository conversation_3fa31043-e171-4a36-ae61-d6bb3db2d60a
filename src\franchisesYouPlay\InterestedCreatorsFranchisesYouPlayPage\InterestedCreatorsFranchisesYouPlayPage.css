.mg-ic-ui-primary-franchise-title {
  @apply pb-meas5 md:pb-meas4 text-left md:text-center font-display-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}
.mg-ic-ui-primary-franchise-subtitle {
  @apply mb-meas16 text-left md:text-center font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.mg-ic-ui-primary-franchise-container {
  @apply flex w-full flex-col items-center justify-center border-b border-t border-[rgba(255,255,255,0.33)] py-meas16;
}
.mg-ic-ui-primary-franchise-container .select-box {
  @apply w-[317px];
}
.mg-ic-ui-sc-franchise-container {
  @apply flex w-full md:w-[635px] flex-col items-start md:items-center justify-center overflow-hidden pt-meas16;
}
.ic-ui-primary-franchise-selected {
  border: 4px solid rgba(115, 204, 117) !important;
  border-radius: 4px;
}
.ic-ui-primary-franchise-option {
  @apply mt-meas28;
}
.secondary-franchise-options {
  @apply grid grid-cols-2 gap-meas8 md:grid-cols-4 md:gap-meas10 xl:gap-meas6;
}
.secondary-franchise-checkbox-card-container {
  @apply max-h-[180px] md:min-w-[150px] xl:max-h-[280px] xl:min-w-[240px];
}
.ic-ui-primary-franchise-option-image {
  @apply h-[200px] w-[200px] md:h-[262px] md:w-[262px] xl:h-[262px] xl:w-[262px];
}
.ic-ui-secondary-franchise-load-more {
  @apply md:w-full mb-meas16 flex justify-center mx-auto;
}
.ic-ui-secondary-franchise-load-more > .btn-secondary {
  @apply md:w-full md:max-w-[635px];
}
.mg-ic-ui-secondary-franchise-disabled {
  opacity: 0.2;
}
.mg-ic-ui-page-interested-creator .interested-creators-ui-franchises-container form {
  @apply m-auto w-[100%];
}
.interested-creators-ui-franchises-container .card-title {
  @apply xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small xl:text-desktop-body-default;
}
