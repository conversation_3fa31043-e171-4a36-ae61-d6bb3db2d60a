export const franchisesYouPlayLabels = {
  franchisesYouPlay: {
    confirmationDesc1: "confirmationDesc1",
    confirmationDesc2: "conf'irmationDesc2",
    modalConfirmationTitle: "modalConfirmationTitle",
    title: "Franchises you Play",
    description:
      "Tell us about the Electronic Arts franchises that you enjoy playing. This will tell us what opportunities to show you. You can always change them or add more in your profile settings.",
    buttons: {
      yes: "Yes",
      no: "No",
      cancel: "Cancel",
      next: "Next",
      submit: "Submit",
      close: "Close"
    }
  },
  layout: {
    buttons: {
      yes: "Yes",
      no: "No",
      cancel: "Cancel",
      next: "Next",
      submit: "Submit",
      close: "Close"
    },
    main: {
      unhandledError: "unhandledError"
    }
  },
  franchisesYouPlayFormLabels: {
    primaryFranchiseTitle: "Franchise you play the most",
    primaryFranchiseSubTitle: "Select the one that you enjoy the most or spend the most time in.",
    secondaryFranchiseTitle: "Select more Franchises",
    secondaryFranchiseSubTitle: "You can choose as many as you like",
    labels: {
      primaryFranchise: "Primary Franchise",
      loadMore: "Load more..."
    },
    messages: {
      primaryFranchise: "primaryFranchise"
    },
    buttons: {
      yes: "Yes",
      no: "No",
      cancel: "Cancel",
      next: "Next",
      submit: "Submit",
      close: "Close"
    }
  }
};
