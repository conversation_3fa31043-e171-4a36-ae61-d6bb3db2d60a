import CancelRegistrationModal from "../../CancelRegistrationModal/CancelRegistrationModal";
import InterestedCreatorInformationForm, {
  FormLabels
} from "../InterestedCreatorInformationForm/InterestedCreatorInformationForm";
import { Toast } from "@eait-playerexp-cn/core-ui-kit";
import {
  BrowserAnalytics,
  CloseHandler,
  Dispatch,
  DOMAIN_ERROR,
  ERROR,
  ErrorHandling,
  getExtractedErrorMessage,
  LOADING,
  onToastClose,
  State,
  toastContent,
  VALIDATION_ERROR,
  ValidationError
} from "../../utils";
import ConnectedAccountsService from "../../Browser/ConnectedAccountsService";
import {
  GET_FB_PAGES,
  RELOAD_INTERESTED_CREATOR_ACCOUNTS
} from "../ConnectAccounts/ConnectedAccount/ConnectedAccounts";
import React, { memo, ReactElement, ReactNode, useCallback, useEffect, useMemo, useState } from "react";
import { Configuration, ConnectAccounts, Fbpages, Information, Rules } from "../Information";
import { NextRouter } from "next/router";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

export const YOUTUBE_NO_CHANNEL_ERROR = "save-you-tube-account-unknown-connected-account";
export const INSTAGRAM_STEPS_LINK =
  "https://business.instagram.com/getting-started?fbclid=IwAR2RCQ_lweAva29YXvt7Nfa2wDshHe9oT5LjbHXbGpSKzhIs56G5gfQlrUk";
export const INSTA_WARNING_ERROR = "save-instagram-account-unknown-instagram-business-account";
export const INSTA_CANNOT_CONNECT = "save-instagram-account-cannot-connect-account";
export type Country = {
  value: string;
  label: string;
  name: string;
};
export type Language = {
  value: string;
  label: string;
  name?: string;
};
export type Locale = {
  value: string;
  label: string;
  id?: string;
};
export type PreferredLanguage = {
  code: string;
  name: string;
};
export type Countries = Array<Country>;
export type Languages = Array<Language>;
export type Locales = Array<Locale>;

export type PageLabels = {
  confirmationDesc1: string;
  confirmationDesc2: string;
  modalConfirmationTitle: string;
  interestedCreatorTitle: string;
  yes: string;
  no: string;
  interestedUserDescription1: string;
  interestedUserDescription2: string;
};

export type ConnectAccountLabels = {
  title: string;
  message: string;
  subTitle: string;
  myAccount: string;
  addAccount: string;
  description: string;
  accounts: {
    youTube: string;
    facebook: string;
    twitch: string;
    instagram: string;
    tiktok: string;
  };
  modalTitle: string;
  modalMessage: string;
  modalConfirmationTitle: string;
  modalConfirmationTitleFB: string;
  confirmationDesc1: string;
  confirmationDesc2: string;
  messages: {
    removeAccountTitle: string;
    removeAccountDescription: string;
    cannotConnectInstaAccount: string;
    cannotConnectInstaAccountHeader: string;
    actionTitle: string;
    actionDescription1: string;
    actionDescription2: string;
    actionDescription3: string;
    actionDescription4: string;
    youtubeNoChannelError: string;
  };
  modal: {
    removeAccountTitle: string;
    removeAccountDescription1: string;
    removeAccountDescription2: string;
  };
  buttons: {
    cancel: string;
    remove: string;
  };
  subscribers: string;
  removeAccount: string;
  expireAccount: string;
  or: string;
  reconnectAccount: string;
  connectNewAccount: string;
  connectNewAccountDescription: string;
  connectNewAccountDescriptionWithTikTok: string;
  myProfile: string;
  information: string;
  gamePreferences: string;
  creatorType: string;
  connectedAccounts: string;
  communicationSettings: string;
  legalDocuments: string;
  paymentInformation: string;
  pointOfContact: string;
  discord: string;
  email: string;
  verificationPending: string;
  reVerifyAccount: string;
  cancel: string;
  close: string;
  connect: string;
  remove: string;
};

export type ModalLabels = {
  title: string;
  description: string;
  cancel: string;
  close: string;
};
export type Layout = {
  main: {
    unhandledError: string;
  };
  buttons: {
    close: string;
  };
};

export type InterestedCreatorsInformationPageProps = {
  formLabels: FormLabels;
  pageLabels: PageLabels;
  showConfirmation?: boolean;
  children?: ReactNode;
  interestedCreator: Information;
  rules: Rules;
  stableDispatch: Dispatch;
  setShowConfirmation: (a: boolean) => void;
  router: NextRouter;
  locale: string;
  handleCancelRegistration: () => void;
  analytics: BrowserAnalytics;
  layout: Layout;
  INTERESTED_CREATOR_REAPPLY_PERIOD: boolean;
  state: State;
  errorHandling: ErrorHandling;
  configuration: Configuration;
  redirectedToNextStepUrl: string;
  onClose: () => void;
  showAddConfirmation: boolean;
  setShowAddConfirmation: (showAddConfirmation: boolean) => void;
  connectAccountLabels: ConnectAccountLabels;
  accountToRemove: string;
  setAccountToRemove: (accountToRemove: string) => void;
  showRemoveAccountModal: boolean;
  setShowRemoveAccountModal: (showRemoveAccountModal: boolean) => void;
  connectAccounts: ConnectAccounts;
  errorToast: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
  warning: (JSXElementConstructor: ReactElement, CloseHandler: CloseHandler) => void;
} & Fbpages;

export type ErrorType = {
  code: string;
  message: string;
};

const REMOVE_ONLY_ACCOUNT = "disconnect-account-conflicting-action";
const REMOVE_ONLY_ACCOUNT_INVALID = "disconnect-account-invalid-input";

export default memo(function InterestedCreatorsInformationPage({
  formLabels,
  pageLabels,
  onClose,
  showConfirmation = false,
  interestedCreator,
  rules,
  stableDispatch,
  setShowConfirmation,
  router,
  locale,
  analytics,
  layout,
  INTERESTED_CREATOR_REAPPLY_PERIOD,
  state,
  handleCancelRegistration,
  errorHandling,
  configuration,
  redirectedToNextStepUrl,
  showAddConfirmation,
  setShowAddConfirmation,
  connectAccountLabels,
  accountToRemove,
  setAccountToRemove,
  showRemoveAccountModal,
  setShowRemoveAccountModal,
  pages,
  connectAccounts,
  errorToast,
  warning
}: // error
InterestedCreatorsInformationPageProps) {
  const {
    confirmationDesc1,
    confirmationDesc2,
    modalConfirmationTitle,
    interestedCreatorTitle,
    interestedUserDescription1,
    interestedUserDescription2
  } = pageLabels;
  const {
    isError = false,
    isValidationError,
    domainError,
    reloadInterestedCreatorAccounts = false,
    getFbPages = false
  } = state as {
    isError: boolean;
    isValidationError: ValidationError[];
    domainError: boolean;
    reloadInterestedCreatorAccounts: boolean;
    getFbPages: boolean;
  };
  const { selectCountry } = formLabels;
  const [countries, setCountries] = useState<Countries>([]);
  const [languages, setLanguages] = useState<Languages>([]);
  const [locales, setLocales] = useState<Array<Locale>>([]);
  const [accounts, setAccounts] = useState([]);
  const [facebookPages, setFacebookPages] = useState(pages);
  const [error, setError] = useState<ErrorType>();
  const {
    main: { unhandledError }
  } = layout;
  const {
    actionTitle,
    actionDescription1,
    actionDescription2,
    actionDescription3,
    actionDescription4,
    cannotConnectInstaAccount,
    cannotConnectInstaAccountHeader,
    removeAccountDescription,
    youtubeNoChannelError
  } = connectAccountLabels.messages;

  const metadataService = new MetadataService(configuration.metadataClient);
  const connectedAccountsService = new ConnectedAccountsService(configuration?.applicationsClient);

  if (INTERESTED_CREATOR_REAPPLY_PERIOD) {
    connectAccountLabels.expireAccount = connectAccountLabels.verificationPending;
    connectAccountLabels.reconnectAccount = connectAccountLabels.reVerifyAccount;
  }

  const instaWarningContent = useMemo(() => {
    return (
      <div className="connect-account-insta-warning">
        <p>{actionDescription1}</p>
        <br />
        <p key="actionDescription2">
          {actionDescription2}{" "}
          <a className="connect-account-insta-steps" target="_blank" rel="noreferrer" href={INSTAGRAM_STEPS_LINK}>
            {actionDescription3}
          </a>
          {actionDescription4}
        </p>
      </div>
    );
  }, [actionDescription1, actionDescription2, actionDescription3, actionDescription4]);

  const clearError = useCallback(async () => {
    try {
      // Unset error from session.
      stableDispatch({ type: LOADING, data: true });
      await connectedAccountsService.deleteConnectedAccountErrors();
      stableDispatch({ type: LOADING, data: false });
    } catch (e) {
      stableDispatch({ type: LOADING, data: false });
      errorHandling(stableDispatch, e as unknown as Error);
    }
  }, [stableDispatch]);

  const errorMap = useMemo(
    () =>
      new Map([
        [REMOVE_ONLY_ACCOUNT, removeAccountDescription],
        [REMOVE_ONLY_ACCOUNT_INVALID, removeAccountDescription],
        [INSTA_CANNOT_CONNECT, cannotConnectInstaAccount],
        [YOUTUBE_NO_CHANNEL_ERROR, youtubeNoChannelError]
      ]),
    [removeAccountDescription, cannotConnectInstaAccount, youtubeNoChannelError]
  );

  useEffect(() => {
    if (error?.code === INSTA_WARNING_ERROR) {
      warning(<Toast header={actionTitle} content={instaWarningContent} />, {
        onClose: () => clearError()
      });
    } else if (error && errorMap) {
      const header = error?.code === INSTA_CANNOT_CONNECT ? cannotConnectInstaAccountHeader : unhandledError;
      const errorMessage = getExtractedErrorMessage(errorMap, error, unhandledError);
      errorToast(<Toast header={header} content={errorMessage} />, {
        onClose: () => clearError()
      });
    }
  }, [
    error,
    actionTitle,
    instaWarningContent,
    warning,
    stableDispatch,
    errorToast,
    unhandledError,
    errorMap,
    cannotConnectInstaAccountHeader
  ]);

  const fetchFacebookPages = async () => {
    try {
      const response = await connectedAccountsService.getFacebookPages();
      setFacebookPages(response.pages);
    } catch (e) {
      // pages were removed from the session when creator cancel from the pages modal
      console.log(e);
      setFacebookPages([]);
    }
  };

  const fetchConnectedAccounts = async () => {
    try {
      const connectedAccounts = INTERESTED_CREATOR_REAPPLY_PERIOD
        ? await connectedAccountsService.getAllConnectedAccountsWithExpirationStatus(interestedCreator.nucleusId)
        : await connectedAccountsService.getConnectedAccounts(interestedCreator.nucleusId);
      setAccounts(connectedAccounts as []);
      if (getFbPages) {
        await fetchFacebookPages();
      }
      stableDispatch({ type: LOADING, data: false });
    } catch (e) {
      stableDispatch({ type: LOADING, data: false });
      errorHandling(stableDispatch, e as unknown as Error);
    }
  };

  useEffect(() => {
    if (!accountToRemove) {
      stableDispatch({ type: LOADING, data: true });
      fetchConnectedAccounts();
    }
  }, [accountToRemove]);

  useEffect(() => {
    stableDispatch({ type: LOADING, data: true });
    if (reloadInterestedCreatorAccounts) {
      setShowAddConfirmation(false); // re-setting showAddConfirmation value as we are not reloads the page.
      fetchConnectedAccounts();
      fetchInstaErrors();
    }
    stableDispatch({ type: RELOAD_INTERESTED_CREATOR_ACCOUNTS, data: false });
    stableDispatch({ type: GET_FB_PAGES, data: false });
  }, [reloadInterestedCreatorAccounts]);

  const fetchInstaErrors = async () => {
    const error = await connectedAccountsService.getConnectAccountErrors();
    setError(error);
    stableDispatch({ type: LOADING, data: false });
  };

  useEffect(() => {
    async function fetchData() {
      stableDispatch({ type: LOADING, data: true });
      try {
        const countries: Countries = await metadataService.getCountriesMatching();
        if (countries) {
          setCountries([{ value: "", label: selectCountry, name: selectCountry }, ...countries]);
        }
        const languages = await metadataService.getLanguages();
        if (languages) setLanguages(languages);
        const locales = await metadataService.getLocales();
        if (locales) setLocales(locales);
        stableDispatch({ type: LOADING, data: false });
      } catch (e) {
        stableDispatch({ type: LOADING, data: false });
        errorHandling(stableDispatch, e as unknown as Error);
      }
    }
    fetchData();
  }, [selectCountry, stableDispatch]);

  useEffect(() => {
    if (isError || isValidationError) {
      errorToast(<Toast header={unhandledError} content={isError ? isError : toastContent(isValidationError)} />, {
        onClose: () => onToastClose(isError ? ERROR : VALIDATION_ERROR, stableDispatch)
      });
    }
  }, [isError, isValidationError, stableDispatch, unhandledError, errorToast]);

  useEffect(() => {
    if (domainError) {
      const errorMessage = getExtractedErrorMessage(errorMap, domainError, unhandledError);
      errorToast(<Toast header={unhandledError} content={errorMessage} />, {
        onClose: () => onToastClose(DOMAIN_ERROR, stableDispatch)
      });
    }
  }, [domainError, unhandledError, stableDispatch, errorToast, errorMap, layout.buttons.close]);

  const handleModalClose = useCallback(() => setShowConfirmation(false), [setShowConfirmation]);

  useEffect(() => {
    if (INTERESTED_CREATOR_REAPPLY_PERIOD) {
      if (!interestedCreator.country == undefined && interestedCreator.countryCode) {
        interestedCreator = {
          ...interestedCreator,
          country: countries.find(({ value }) => value === interestedCreator.countryCode)
        };
      }
      if (
        !interestedCreator.contentUrls &&
        interestedCreator?.contentAccounts &&
        interestedCreator?.contentAccounts?.length > 0
      ) {
        interestedCreator.contentUrls = interestedCreator.contentAccounts;
      }
    }
  }, [countries, interestedCreator]);

  const modalLabels = {
    title: modalConfirmationTitle,
    yes: pageLabels.yes,
    no: pageLabels.no,
    close: layout.buttons.close,
    confirmationDesc1: confirmationDesc1,
    confirmationDesc2: confirmationDesc2
  };

  return (
    <div className="mg-ic-ui-page-interested-creator">
      <div className="mg-ic-ui-intro">
        <h3 className="mg-ic-ui-intro-title">{interestedCreatorTitle}</h3>
      </div>
      <div className="interested-creators-ui-information-description">
        <p className="interested-creators-ui-information-sub-description">
          {interestedUserDescription1} <strong className="gamer-tag">{interestedCreator.defaultGamerTag}!</strong>{" "}
          {interestedUserDescription2}
        </p>
      </div>
      <InterestedCreatorInformationForm
        formLabels={formLabels}
        onClose={onClose}
        languages={languages}
        locales={locales}
        countries={countries}
        interestedCreator={interestedCreator}
        rules={rules}
        router={router}
        locale={locale}
        stableDispatch={stableDispatch}
        analytics={analytics}
        state={state}
        errorHandling={errorHandling}
        configuration={configuration}
        redirectedToNextStepUrl={redirectedToNextStepUrl}
        showAddConfirmation={showAddConfirmation}
        setShowAddConfirmation={setShowAddConfirmation}
        connectAccountLabels={connectAccountLabels}
        accountToRemove={accountToRemove}
        setAccountToRemove={setAccountToRemove}
        showRemoveAccountModal={showRemoveAccountModal}
        setShowRemoveAccountModal={setShowRemoveAccountModal}
        accounts={accounts}
        pages={facebookPages}
        INTERESTED_CREATOR_REAPPLY_PERIOD={INTERESTED_CREATOR_REAPPLY_PERIOD}
        connectAccounts={connectAccounts}
      />
      {showConfirmation && (
        <CancelRegistrationModal
          labels={modalLabels}
          handleCancelRegistration={handleCancelRegistration}
          handleModalClose={handleModalClose}
        />
      )}
    </div>
  );
});