export const informationLabels = {
  title: "Start Building your Profile",
  interestedCreatorTitle: "Start your submission",
  interestedUserDescription1: "Hi there",
  interestedUserDescription2: "We need some basic information from you. First off, tell us who you are.",
  infoTitle: "Your Information",
  infoSubTitle: "Game Changer since:",
  primaryPlatform: "Primary Platform",
  platformPreferences: "Platform Preferences",
  platformPreferencesTitle: "Select the platform you play the most.",
  secondaryPlatforms: "Secondary Platform",
  secondaryPlatformsTitle: "Select other platforms that you own.",
  modalConfirmationTitle: "Are you sure you want to cancel registration?",
  confirmationDesc1: "Pressing Yes will quit the registration process.",
  confirmationDesc2: "You can begin registration again at any point by visiting the Creator Network Website.",
  personalInformation: "Personal Information",
  mailingAddress: "Mailing Address",
  miscellaneous: "Miscellaneous",
  creatorSince: "Creator Since",
  payableStatus: "Payable",
  nonPayableStatus: "Payment Details Incomplete",
  nonPayableStatusHelp: "You need to provide payment information in order to become payable.",
  legalEntityType: "Legal Entity & Address",
  legalEntityDescription:
    "This info will also be used for payment purposes. Changing your entity type or address may require you to update your payment/tax info in the Payment Information page.",
  success: {
    updatedInformationHeader: "Information update successful",
    personalInformation: "You have successfully updated your Personal Information.",
    mailingAddress: "You have successfully updated your Mailing Address.",
    miscellaneous: "You have successfully updated your Miscellaneous Information.",
    legalEntityType: "You have successfully updated your Legal Entity Information.",
    platformPreferences: "You have successfully updated your Platform Preferences."
  },
  labels: {
    none: "None",
    preferredEmail: "Preferred Email for Communication",
    firstName: "First Name",
    lastName: "Last Name",
    EAID: "Electronic Arts ID",
    EAEmail: "Electronic Arts Account Email",
    dateOfBirth: "Date of Birth",
    country: "Country/Region",
    street: "Street",
    addressLine2: "labels.addressLine2",
    city: "City",
    state: "State or Province",
    zipCode: "Zip Code or Postal Code",
    primaryPlatform: "Select a Platform",
    tShirtSize: "T-Shirt Size",
    hardwarePartners: "Hardware Partners",
    entityType: "Entity Type",
    individual: "Individual",
    business: "Business",
    businessName: "Name of Business",
    legalAddressAsMailingAddress: "Address is the same as my mailing address.",
    contentMediaTitle: "Social Media Or Content Links",
    contentMediaDescription:
      "Please enter the URL of at least one of your social media accounts or website with your content. This will help our team assess our compatibility. You can add up to 10 links e.g. YouTube, Twitch, Facebook, Instagram, etc.",
    contentUrl: "Social Media or Website URL (Required)",
    contentFollowers: "Followers(optional)",
    contentUrlPlaceholder: "e.g. https://www.youtube.com/user/SuperAwesome123",
    contentFollowersPlaceholder: "No. of followers",
    contentLanguage: "Content Language(s)",
    selectCountry: "Select your country"
  },
  messages: {
    firstName: "First Name is required",
    firstNameTooLong: "First Name is too long",
    lastName: "Last Name is required",
    lastNameTooLong: "Last Name is too long",
    dateOfBirth: "Date of Birth is required",
    dateOfBirthInvalid: "Date of Birth is invalid",
    ageMustBe18OrOlder: "Must be 18 or older",
    country: "Country/Region is required",
    street: "Street is required",
    streetTooLong: "Street is too long",
    city: "City is required",
    cityTooLong: "City is too long",
    state: "State or Province is required",
    stateTooLong: "State is too long",
    zipCode: "Zip Code or Postal Code is required",
    zipCodeTooLong: "Zip Code or Postal Code is too long",
    primaryPlatform: "Primary platform is required",
    secondaryPlatforms: "messages.secondaryPlatforms",
    tShirtSize: "T-Shirt Size is required",
    hardwarePartners: "Hardware Partners is required",
    entityType: "Entity Type is required",
    businessName: "Business Name is required",
    businessNameTooLong: "Business Name is too long",
    email: "Email Address is required",
    emailTooLong: "Email Address is too long",
    emailInvalid: "Email Address is invalid",
    url: "URL is required",
    invalidUrl: "URL provided is invalid",
    duplicateUrl: "Duplicate URLs not allowed",
    urlScanFailed: "You cannot submit content from this website/domain at this time.",
    followersMaxLength: "Maximum 18 digits allowed"
  },
  profilePicture: {
    title: "Change My Avatar",
    message: "Select an image from your computer (JPEG, PNG or GIF). Image should be square and less than 1MB.",
    termsAndConditionsFirst:
      'Respect the rights of others. Only upload images that you own or that you have written permission to freely distribute. Like most other online services, your avatar will be visible to other users of the service and associated with your Electronic Arts ID, even if your profile is set to "no one can see." Please read our ',
    termsAndConditionsMiddle: "User Agreement",
    termsAndConditionsLast: "for more information.",
    avatarRequired: "Please select an image",
    avatarInvalid: "Please select valid image",
    avatarMoreThanLimit: "Image size should be less than 1MB"
  },
  info: {
    businessName: "Only if you are contracting under a business entity; otherwise, leave blank."
  },
  translation: {
    messages: {
      preferredEmailTooLong: "Preferred Email is too long",
      preferredEmailInvalid: "Preferred Email is invalid",
      preferredPhoneNumber: "Preferred Phone Number",
      preferredPhoneNumberTooLong: "Preferred Phone Number is too long",
      contentLanguage: "Content Language",
      language: "Language",
      preferredEmail: "Preferred Email"
    }
  },
  infoLabels: {
    interestedCreatorTitle: "Start your submission",
    messages: {
      firstName: "First Name is required",
      firstNameTooLong: "First Name is too long",
      lastName: "Last Name is required",
      lastNameTooLong: "Last Name is too long",
      dateOfBirth: "Date of Birth is required",
      dateOfBirthInvalid: "Date of Birth is invalid",
      ageMustBe18OrOlder: "Must be 18 or older",
      country: "Country/Region is required",
      street: "Street is required",
      streetTooLong: "Street is too long",
      city: "City is required",
      cityTooLong: "City is too long",
      state: "State or Province is required",
      stateTooLong: "State is too long",
      zipCode: "Zip Code or Postal Code is required",
      zipCodeTooLong: "Zip Code or Postal Code is too long",
      primaryPlatform: "Primary platform is required",
      secondaryPlatforms: "messages.secondaryPlatforms",
      tShirtSize: "T-Shirt Size is required",
      hardwarePartners: "Hardware Partners is required",
      entityType: "Entity Type is required",
      businessName: "Business Name is required",
      businessNameTooLong: "Business Name is too long",
      email: "Email Address is required",
      emailTooLong: "Email Address is too long",
      emailInvalid: "Email Address is invalid",
      url: "URL is required",
      invalidUrl: "URL provided is invalid",
      duplicateUrl: "Duplicate URLs not allowed",
      urlScanFailed: "You cannot submit content from this website/domain at this time.",
      followersMaxLength: "Maximum 18 digits allowed"
    }
  },
  layout: {
    main: {
      unhandledError: "Unhandled Error"
    },
    buttons: {
      close: "Close"
    }
  }
};
