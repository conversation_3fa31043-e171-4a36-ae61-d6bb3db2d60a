.mg-ic-ui-connected-accounts-container {
  @apply my-meas16 flex w-[288px] flex-col border-t border-[rgba(255,255,255,0.33)] pt-meas16 md:w-[635px] md:items-center;
}

.ic-ui-myprofile-view > .mg-ic-ui-connect-accounts > h4 {
  @apply pb-meas16 font-display-bold font-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4;
}

.ic-ui-myprofile-view > .mg-ic-ui-connect-accounts > .mg-ic-ui-connect-accounts-title {
  @apply xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5;
}

.ic-ui-myprofile-view .mg-ic-ui-connected-accounts-container {
  @apply items-center max-w-[635px];
}

.ic-ui-myprofile-view .mg-ic-ui-connected-accounts-title {
  @apply self-start xs:text-mobile-body-large md:text-tablet-body-large lg:text-desktop-body-large;
}

.mg-ic-ui-connected-accounts-title {
  @apply pb-meas16 font-display-bold xs:text-mobile-h5 md:self-center md:text-tablet-h5 lg:text-desktop-h5 xl:self-start;
}

.ic-ui-connect-account-card-container {
  @apply grid grid-cols-2 md:grid-cols-3;
}
.account-card-container {
  @apply w-[138px] min-h-[180px] md:w-[197px] md:min-h-[193px];
}
.btn-secondary.btn-dark {
  @apply flex items-center justify-center;
}
.account-card-add-icon {
  @apply hidden;
}
.account-card-add-account-btn {
  @apply text-[13px];
}
.ic-ui-myprofile-view .ic-ui-connect-account-card-container {
  @apply grid grid-cols-2 gap-meas10 md:grid-cols-3 md:gap-meas15;
}

.cancel-registration-modal-confirmation-additional-desc,
.disconnect-account-modal-confirmation-additional-desc {
  @apply mt-meas7;
}
.account-card-add-spacing {
  @apply flex items-center justify-center mt-meas0;
}
.account-card-username {
  @apply font-display-bold text-[13px];
}
.account-card-remove-text {
  @apply text-[10px];
}
