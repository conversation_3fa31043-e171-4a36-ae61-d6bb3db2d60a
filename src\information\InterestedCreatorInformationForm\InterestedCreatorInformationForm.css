.mg-ic-ui-form-container {
  @apply flex w-full flex-col justify-between border-t border-[rgba(255,255,255,0.33)] pt-meas16;
}
.mg-ic-ui-form-container .input-box-label,
.mg-ic-ui-form-container .select-label {
  @apply text-gray-10;
} /* Adding parent class for .select-label as we should not override core-ui-kit select label without parent class. It will impact globally*/
.mg-ic-ui-form-container .select-header-title,
.select-header-label {
  @apply w-full;
}
.information-form {
  @apply grid grid-cols-1 gap-y-meas10 md:grid-cols-2 md:gap-x-meas20 md:gap-y-meas12;
}
.information {
  @apply flex flex-1 flex-col;
}

.information-title {
  @apply font-display-bold xs:text-mobile-h4 md:text-tablet-h4 lg:text-desktop-h4 font-bold text-gray-10 text-center;
}
.information-subtitle {
  @apply pt-[10px] font-text-regular xs:text-mobile-body-small md:text-tablet-body-small lg:text-desktop-body-small;
}
.information-avatar {
  @apply mb-meas16 mt-[34px] flex justify-center md:mb-meas0 md:mt-meas0 md:justify-end md:self-end;
}
.mg-ic-ui-platform-container {
  @apply my-meas16 flex w-full flex-col items-center justify-center border-b border-t border-[rgba(255,255,255,0.33)] py-meas16 md:w-[635px];
}
.mg-ic-ui-platform-title {
  @apply pb-meas10 font-display-regular xs:text-mobile-h3 md:text-tablet-h3 lg:text-desktop-h3 font-bold;
}
.mg-ic-ui-platform-description {
  @apply font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.mg-ic-ui-platform-container .empty-card {
  @apply mt-meas8;
}
.mg-ic-ui-platform-container .select-box {
  @apply mb-[12.97px] h-meas20 w-[290px] w-full md:mb-[13.55px] md:w-[320px];
}
.mg-ic-ui-platform-container .select-box .select-header {
  @apply h-meas20;
}
.mg-ic-ui-platform-container .select-header-title,
.select-header-label {
  @apply w-full;
}
.mg-ic-ui-platform-container .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.mg-ic-ui-sc-platform-container {
  @apply flex w-full flex-col items-center justify-center overflow-hidden xl:w-[1070px];
}
.mg-ic-ui-sc-platform-container .mg-ic-ui-intro {
  @apply mb-[60px];
}
.mg-ic-ui-secondary-title {
  @apply pb-meas10 font-display-regular xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5;
}
.mg-ic-ui-secondary-title[data-disabled="true"] {
  @apply opacity-50;
}
.mg-ic-ui-form-container .input-box,
.select-header-label,
.from-text-field {
  @apply xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.mg-ic-ui-form-container .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.information-field {
  @apply flex min-h-[2.5rem] items-center font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default text-gray-10;
}

.communication-preferences-container {
  @apply w-11/12 md:w-[635px];
}
.communication-preferences-container .mg-ic-ui-intro {
  @apply w-11/12 md:w-[590px];
}
.communication-preferences-container > form {
  width: 100% !important;
}
.mg-ic-ui-communication-row {
  @apply flex w-full flex-col items-start justify-start border-t border-[rgba(255,255,255,0.33)] py-meas16;
}
.mg-ic-ui-communication-row > .form-input-box,
.mg-ic-ui-communication-row > .select-box {
  @apply mx-meas0 w-full;
}
.mg-ic-ui-communication-row .input-box-label,
.mg-ic-ui-communication-row .select-label {
  @apply font-text-regular xs:text-mobile-caption1 md:text-tablet-caption1 lg:text-desktop-caption1 text-gray-10;
} /* Adding parent class for .select-label as we should not override core-ui-kit select label without parent class. It will impact globally*/
.mg-ic-ui-communication-title {
  @apply flex flex-row pb-meas6 font-display-regular xs:text-mobile-h5 md:text-tablet-h5 lg:text-desktop-h5;
}
.mg-ic-ui-communication-description {
  @apply flex flex-row pb-meas18 font-text-regular xs:text-mobile-body-default md:text-tablet-body-default lg:text-desktop-body-default;
}
.mg-ic-ui-communication-row .select-header {
  @apply h-meas20;
}
.mg-ic-ui-communication-row .select-header-title,
.select-header-label {
  @apply w-full;
}
.mg-ic-ui-communication-row .select-list,
.select-scroll-list {
  @apply w-[99.7%];
}
.btn-discord > .icon-block .icon {
  @apply h-meas10 w-meas10;
}
.mg-ic-ui-communication-row.preferred-email {
  @apply pb-[30px] pt-meas22;
}
.mg-ic-ui-communication-row.preferred-phone-number,
.mg-ic-ui-communication-row.language {
  @apply py-meas16;
}
.mg-ic-ui-communication-title.preferred-phone-number,
.mg-ic-ui-communication-description.content-language,
.mg-ic-ui-communication-description.language {
  @apply pb-meas0 text-gray-10;
}
.mg-ic-ui-communication-row.content-language {
  @apply py-meas16;
}
.mg-ic-ui-communication-description.preferred-email {
  @apply pb-meas13;
}
.mg-ic-ui-communication-row.discord .mg-ic-ui-connected-accounts-container {
  border-top: none;
}

.mg-ic-ui-communication-row.discord > button {
  @apply flex items-center justify-center;
}

.mg-ic-ui-communication-row.discord > .btn-dark > span {
  @apply ml-meas4;
}

.mg-ic-ui-communication-row.discord .mg-ic-ui-connected-accounts-container .mg-ic-ui-connected-accounts-title {
  @apply hidden;
}

.mg-ic-ui-communication-row.discord .ic-ui-connected-acc-card-container .empty-card {
  box-shadow:
    rgba(115, 204, 117, 0.8) 0 1px 4px,
    rgba(115, 204, 117, 0.8) 0 0 1px 4px;
  width: 231px;
  height: 231px;
}
