import { type TraceableHttpClient } from "@eait-playerexp-cn/http-client";
import InterestedCreatorsServices, { InterestedCreator } from "./InterestedCreatorsService";
import { anInterestedCreator } from "../../test/factories/InterestedCreatorFactories";

jest.mock("@eait-playerexp-cn/http-client");

describe("InterestedCreatorsServices", () => {
  it("shows a intrested creator application save", async () => {
    const client = { post: jest.fn() } as unknown as TraceableHttpClient;
    const service = new InterestedCreatorsServices(client);
    const interestedCreator = anInterestedCreator();

    await service.saveApplication(interestedCreator as InterestedCreator);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith(`/api/applications`, { body: interestedCreator });
  });

  it("saves an interested creator information", async () => {
    const client = { post: jest.fn() } as unknown as TraceableHttpClient;
    const service = new InterestedCreatorsServices(client);
    const interestedCreator = anInterestedCreator();

    await service.saveInterestedCreatorInformation(interestedCreator as InterestedCreator);

    expect(client.post).toHaveBeenCalledTimes(1);
    expect(client.post).toHaveBeenCalledWith(`/api/v4/interested-creators`, { body: interestedCreator });
  });
});
