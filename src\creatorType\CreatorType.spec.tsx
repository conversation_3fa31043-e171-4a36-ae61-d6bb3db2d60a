import React, { act } from "react";
import { render, screen } from "@testing-library/react";
import { NextRouter, useRouter } from "next/router";
import { creatorType, franchisesYouPlay, information } from "@eait-playerexp-cn/core-ui-kit";
import { BrowserAnalytics } from "../utils";
import { creatorTypeLabel } from "../../test/translations/CreatorType";
import { interestedCreatorsCreatorTypeFormLabels } from "../../test/translations/InterestedCreatorsCreatorTypeForm";
import { aCreatorType } from "@eait-playerexp-cn/metadata-test-fixtures";
import CreatorType from "./CreatorType";
import { axe } from "jest-axe";
import InterestedCreatorsCreatorTypePage from "./InterestedCreatorsCreatorTypePage/InterestedCreatorsCreatorTypePage";
import { Configuration } from "../information/Information";
import { MetadataService } from "@eait-playerexp-cn/metadata-http-client";

jest.mock("@eait-playerexp-cn/metadata-http-client");
jest.mock("next/router", () => ({
  useRouter: jest.fn()
}));
jest.mock("./InterestedCreatorsCreatorTypePage/InterestedCreatorsCreatorTypePage", () => {
  return jest.fn(() => <div data-testid="interested-creators-ui-creator-type-page" />);
});

describe("InterestedCreatorsCreatorTypes", () => {
  const interestedCreator = {
    nucleusId: 1234567890,
    defaultGamerTag: "",
    originEmail: "",
    dateOfBirth: undefined,
    contentUrls: [{ url: "", followers: "" }],
    creatorTypes: []
  };

  const router = { locale: "en-us", push: jest.fn() };
  const steps = [
    {
      icon: information,
      title: "Information",
      href: "/interested-creators/information",
      isCompleted: false
    },
    {
      icon: creatorType,
      title: "Creator Type",
      href: "/interested-creators/creator-types",
      isCompleted: false
    },
    {
      icon: franchisesYouPlay,
      title: "Franchises You Play",
      href: "/interested-creators/franchises-you-play",
      isCompleted: false
    }
  ];
  const configuration = { metadataClient: {}, applicationsClient: {} } as Configuration;
  const interestedCreatorsCreatorTypesProps = {
    interestedCreator,
    handleCancelRegistration: jest.fn(),
    analytics: {} as unknown as BrowserAnalytics,
    INTERESTED_CREATOR_REAPPLY_PERIOD: false,
    state: {
      onboardingSteps: steps
    },
    stableDispatch: jest.fn(),
    labels: { ...creatorTypeLabel, ...interestedCreatorsCreatorTypeFormLabels },
    errorHandling: jest.fn(),
    redirectedToNextStepUrl: "/interested-creators/franchises-you-play",
    onClose: jest.fn(),
    showConfirmation: true,
    setShowConfirmation: jest.fn(),
    configuration: configuration,
    router: router as unknown as NextRouter,
    locale: "en-us",
    errorToast: jest.fn(),
    basePath: "/support-a-creator"
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockImplementation(() => router);
    (MetadataService as jest.Mock).mockReturnValue({
      getCreatorTypes: jest.fn().mockResolvedValue([aCreatorType({ value: "YouTuber", label: "YouTuber" })])
    });
  });

  it("renders the InterestedCreatorsFranchisesYouPlayPage component", () => {
    render(<CreatorType {...interestedCreatorsCreatorTypesProps} />);

    expect(screen.getByTestId("interested-creators-ui-creator-type-page")).toBeInTheDocument();
  });

  it("passes the correct props to InterestedCreatorsCreatorTypePage", () => {
    render(<CreatorType {...interestedCreatorsCreatorTypesProps} />);

    expect(InterestedCreatorsCreatorTypePage).toHaveBeenCalledWith(
      expect.objectContaining({
        interestedCreator: interestedCreatorsCreatorTypesProps.interestedCreator,
        stableDispatch: interestedCreatorsCreatorTypesProps.stableDispatch,
        errorToast: interestedCreatorsCreatorTypesProps.errorToast,
        formLabels: interestedCreatorsCreatorTypesProps.labels,
        pageLabels: interestedCreatorsCreatorTypesProps.labels,
        unhandledError: interestedCreatorsCreatorTypesProps.labels.unhandledError,
        state: interestedCreatorsCreatorTypesProps.state,
        router: interestedCreatorsCreatorTypesProps.router,
        analytics: interestedCreatorsCreatorTypesProps.analytics,
        INTERESTED_CREATOR_REAPPLY_PERIOD: interestedCreatorsCreatorTypesProps.INTERESTED_CREATOR_REAPPLY_PERIOD,
        errorHandling: interestedCreatorsCreatorTypesProps.errorHandling,
        redirectedToNextStepUrl: interestedCreatorsCreatorTypesProps.redirectedToNextStepUrl,
        configuration: interestedCreatorsCreatorTypesProps.configuration,
        handleCancelRegistration: interestedCreatorsCreatorTypesProps.handleCancelRegistration
      }),
      {}
    );
  });

  it("is accessible", async () => {
    const { container } = render(<CreatorType {...interestedCreatorsCreatorTypesProps} />);

    const results = await act(() => axe(container));

    expect(results).toHaveNoViolations();
  });
});
