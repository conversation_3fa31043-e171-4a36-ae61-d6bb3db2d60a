import React, { ComponentType, ReactElement } from "react";
import { <PERSON>a, StoryObj } from "@storybook/react";
import { NextRouter } from "next/router";
import FranchisesYouPlay from "./FranchisesYouPlay";
import { TraceableHttpClient } from "@eait-playerexp-cn/http-client";

const meta: Meta<typeof FranchisesYouPlay> = {
  title: "Component Library/Franchises You Play Page",
  component: FranchisesYouPlay,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof FranchisesYouPlay>;

const dispatch = (): void => {};

export const FranchisesYouPlayPage: Story = {
  args: {
    handleCancelRegistration: () => {},
    basePath: "/support-a-creator",
    redirectedToNextStepUrl: "/information/completed",
    interestedCreator: {
      defaultGamerTag: "Gamer123",
      nucleusId: 987654321,
      firstName: "<PERSON>",
      lastName: "Doe",
      originEmail: "<EMAIL>",
      dateOfBirth: "1990-01-01",
      country: { label: "United States", name: "United States", value: "US" }
    },
    analytics: { checkedApplicationStatus: () => {} },
    INTERESTED_CREATOR_REAPPLY_PERIOD: true,
    state: {
      exceptionCode: null,
      sessionUser: null,
      isLoading: false
    },
    labels: {
      franchisesYouPlay: {
        confirmationDesc1: "confirmationDesc1",
        confirmationDesc2: "confirmationDesc2",
        modalConfirmationTitle: "modalConfirmationTitle",
        title: "Franchises you Play",
        description:
          "Tell us about the Electronic Arts franchises that you enjoy playing. This will tell us what opportunities to show you. You can always change them or add more in your profile settings.",
        buttons: {
          yes: "Yes",
          no: "No",
          cancel: "Cancel",
          next: "Next",
          submit: "Submit",
          close: "Close"
        }
      },
      franchisesYouPlayFormLabels: {
        primaryFranchiseTitle: "Franchise you play the most",
        primaryFranchiseSubTitle: "Select the one that you enjoy the most or spend the most time in.",
        secondaryFranchiseTitle: "Select more Franchises",
        secondaryFranchiseSubTitle: "You can choose as many as you like",
        labels: {
          primaryFranchise: "Primary Franchise",
          loadMore: "Load more..."
        },
        messages: {
          primaryFranchise: "primaryFranchise"
        }
      },
      layout: {
        buttons: {
          yes: "Yes",
          no: "No",
          cancel: "Cancel",
          next: "Next",
          submit: "Submit",
          close: "Close"
        },
        main: {
          unhandledError: "unhandledError"
        }
      }
    },
    onClose: () => {},
    errorHandling: () => {},
    stableDispatch: dispatch,
    setShowConfirmation: (_a: boolean) => {},
    showConfirmation: false,
    franchisesYouPlayFallbackImages: {
      primaryFranchisefallbackImage: "./img/franchises-you-play/franchise-unselected.png",
      secondaryFranchisefallbackImage: "./img/franchises-you-play/sec-franchise-unselected.png"
    },
    locale: "en-us",
    router: {
      locale: "en-us",
      push: () => Promise.resolve(true)
    } as unknown as NextRouter,
    configuration: {
      metadataClient: {
        get: () =>
          Promise.resolve({
            data: [
              {
                value: "a0G7c000006GAZyEAO",
                label: "NFS Heat",
                image: "./img/franchises/NFS-Heat.png"
              },
              {
                value: "a0G7c000006GAZnEAO",
                label: "Sims 4",
                image: "./img/franchises/Sims-4.png"
              },
              {
                value: "a0G7c000006GAa8EAG",
                label: "Apex",
                image: "./img/franchises/Apex.png"
              },
              {
                value: "a0G7c000006GAaEEAW",
                label: "UFC 4",
                image: "./img/franchises/UFC-4.png"
              },
              {
                value: "a0G7c000006GAa6EAG",
                label: "NHL 21",
                image: "./img/franchises/NHL-21.png"
              },
              {
                value: "a0G7c000006GAa3EAG",
                label: "Madden 21",
                image: "./img/franchises/Madden-21.png"
              },
              {
                value: "a0G7c000006GAa2EAG",
                label: "Dragon Age",
                image: "./img/franchises/dragon-age.jpg"
              },
              {
                value: "a0G7c000006GAaFEAW",
                label: "EA Mobile",
                image: "./img/franchises/ea-no-franchise.png"
              },
              {
                value: "a0G7c000006GAaGEAW",
                label: "EA Originals",
                image: "./img/franchises/ea-originals.png"
              },
              {
                value: "a0G7c000006GAZwEAO",
                label: "F1",
                image: "./img/franchises/f1.jpg"
              },
              {
                value: "a0G7c000006GAZmEAO",
                label: "FIFA",
                image: "./img/franchises/fifa.jpg"
              },
              {
                value: "a0G7c000006GAa1EAG",
                label: "GRID",
                image: "./img/franchises/grid-legends.jpg"
              },
              {
                value: "a0G7c000006GAZoEAO",
                label: "Madden",
                image: "./img/franchises/madden.jpg"
              },
              {
                value: "a0G7c000006GAa7EAG",
                label: "Mass Effect",
                image: "./img/franchises/mass-effect.jpg"
              },
              {
                value: "a0G7c000006GAaAEAW",
                label: "NBA",
                image: "./img/franchises/nba.jpg"
              },
              {
                value: "a0G7c000006GAZqEAO",
                label: "Need for Speed",
                image: "./img/franchises/need-for-speed.jpg"
              },
              {
                value: "a0G7c000006GAaCEAW",
                label: "NHL",
                image: "./img/franchises/nhl.png"
              },
              {
                value: "a0G7c000006GAa0EAG",
                label: "PGA Tour",
                image: "./img/franchises/pga-tour.jpg"
              },
              {
                value: "a0G7c000006GAa9EAG",
                label: "Plants vs Zombies",
                image: "./img/franchises/plants-vs-zombies.jpg"
              },
              {
                value: "a0G7c000006GAa5EAG",
                label: "Real Racing",
                image: "./img/franchises/real-racing.jpg"
              },
              {
                value: "a0G7c000006GAa4EAG",
                label: "Skate",
                image: "./img/franchises/skate.jpg"
              },
              {
                value: "a0G7c000006GAZxEAO",
                label: "Star Wars",
                image: "./img/franchises/star-wars.jpg"
              },
              {
                value: "a0G7c000006GAZpEAO",
                label: "The Sims",
                image: "./img/franchises/the-sims.jpg"
              },
              {
                value: "a0G7c000006GAaDEAW",
                label: "UFC",
                image: "./img/franchises/ufc.png"
              }
            ]
          }),
        patch: () => Promise.resolve()
      } as unknown as TraceableHttpClient,
      applicationsClient: {
        get: () => Promise.resolve([]),
        patch: () => Promise.resolve()
      } as unknown as TraceableHttpClient,
      programCode: "affiliate"
    }
  }
};
