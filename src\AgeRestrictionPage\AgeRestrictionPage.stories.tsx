import React, { ComponentType, ReactElement } from "react";
import { Meta, StoryObj } from "@storybook/react";
import AgeRestrictionPage from "./AgeRestrictionPage";

const meta: Meta<typeof AgeRestrictionPage> = {
  title: "Component Library/Age Restriction Component",
  component: AgeRestrictionPage,
  decorators: [
    (Story: ComponentType): ReactElement => (
      <div className="storybook-dark">
        <Story />
      </div>
    )
  ]
};

export default meta;

type Story = StoryObj<typeof AgeRestrictionPage>;

export const AgeRestrictionComponent: Story = {
  args: {
    labels: {
      close: "Close",
      title: "Sorry!",
      subTitle: "You do not meet the criteria to join the EA Creator Network.",
      bannerImageLabel: "Age Restriction Banner Image"
    },
    ageRestrictionBannerImage: "./img/Players-comp.png",
    onClose: () => {}
  }
};
