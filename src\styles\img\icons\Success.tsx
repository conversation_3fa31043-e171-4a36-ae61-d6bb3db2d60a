import React, { <PERSON> } from "react";
import { SvgProps } from "@eait-playerexp-cn/core-ui-kit";

const SvgSuccess: FC<SvgProps> = (props) => {
  return (
    <svg viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15 8.00435L8.81636 14.188L5 10.3716L6.00435 9.36728L8.81636 12.1793L13.9957 7L15 8.00435Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 18.5C14.6944 18.5 18.5 14.6944 18.5 10C18.5 5.30558 14.6944 1.5 10 1.5C5.30558 1.5 1.5 5.30558 1.5 10C1.5 14.6944 5.30558 18.5 10 18.5ZM10 20C15.5228 20 20 15.5228 20 10C20 4.47715 15.5228 0 10 0C4.47715 0 0 4.47715 0 10C0 15.5228 4.47715 20 10 20Z"
        fill="currentColor"
      />
    </svg>
  );
};

SvgSuccess.defaultProps = {
  className: "icon"
};

export default SvgSuccess;
